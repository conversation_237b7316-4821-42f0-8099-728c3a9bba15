"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_CodeFrame_C"],{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js ***!
  \***************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CodeFrame\", ({\n    enumerable: true,\n    get: function() {\n        return CodeFrame;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _anser = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/anser */ \"./node_modules/next/dist/compiled/anser/index.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"./node_modules/next/dist/compiled/strip-ansi/index.js\"));\nconst _stackframe = __webpack_require__(/*! ../../helpers/stack-frame */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js\");\nconst _useopenineditor = __webpack_require__(/*! ../../helpers/use-open-in-editor */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../hot-linked-text */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/hot-linked-text/index.js\");\nconst CodeFrame = function CodeFrame(param) {\n    _s();\n    let { stackFrame, codeFrame } = param;\n    // Strip leading spaces out of the code frame:\n    const formattedFrame = _react.useMemo(()=>{\n        const lines = codeFrame.split(/\\r?\\n/g);\n        // Find the minimum length of leading spaces after `|` in the code frame\n        const miniLeadingSpacesLength = lines.map((line)=>/^>? +\\d+ +\\| [ ]+/.exec((0, _stripansi.default)(line)) === null ? null : /^>? +\\d+ +\\| ( *)/.exec((0, _stripansi.default)(line))).filter(Boolean).map((v)=>v.pop()).reduce((c, n)=>isNaN(c) ? n.length : Math.min(c, n.length), NaN);\n        // When the minimum length of leading spaces is greater than 1, remove them\n        // from the code frame to help the indentation looks better when there's a lot leading spaces.\n        if (miniLeadingSpacesLength > 1) {\n            return lines.map((line, a)=>~(a = line.indexOf(\"|\")) ? line.substring(0, a) + line.substring(a).replace(\"^\\\\ {\" + miniLeadingSpacesLength + \"}\", \"\") : line).join(\"\\n\");\n        }\n        return lines.join(\"\\n\");\n    }, [\n        codeFrame\n    ]);\n    const decoded = _react.useMemo(()=>{\n        return _anser.default.ansiToJson(formattedFrame, {\n            json: true,\n            use_classes: true,\n            remove_empty: true\n        });\n    }, [\n        formattedFrame\n    ]);\n    const open = (0, _useopenineditor.useOpenInEditor)({\n        file: stackFrame.file,\n        lineNumber: stackFrame.lineNumber,\n        column: stackFrame.column\n    });\n    // TODO: make the caret absolute\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-codeframe\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                    role: \"link\",\n                    onClick: open,\n                    tabIndex: 1,\n                    title: \"Click to open in your editor\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                            children: [\n                                (0, _stackframe.getFrameSource)(stackFrame),\n                                \" @\",\n                                \" \",\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                                    text: stackFrame.methodName\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            viewBox: \"0 0 24 24\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\",\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                                    d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"polyline\", {\n                                    points: \"15 3 21 3 21 9\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"line\", {\n                                    x1: \"10\",\n                                    y1: \"14\",\n                                    x2: \"21\",\n                                    y2: \"3\"\n                                })\n                            ]\n                        })\n                    ]\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"pre\", {\n                children: decoded.map((entry, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                        style: {\n                            color: entry.fg ? \"var(--color-\" + entry.fg + \")\" : undefined,\n                            ...entry.decoration === \"bold\" ? {\n                                fontWeight: 800\n                            } : entry.decoration === \"italic\" ? {\n                                fontStyle: \"italic\"\n                            } : undefined\n                        },\n                        children: entry.content\n                    }, \"frame-\" + index))\n            })\n        ]\n    });\n};\n_s(CodeFrame, \"4Gt+UceGaRqMOFv99GzJu8D2nx8=\");\n_c = CodeFrame;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=CodeFrame.js.map\nvar _c;\n$RefreshReg$(_c, \"CodeFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/index.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/index.js ***!
  \***********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CodeFrame\", ({\n    enumerable: true,\n    get: function() {\n        return _CodeFrame.CodeFrame;\n    }\n}));\nconst _CodeFrame = __webpack_require__(/*! ./CodeFrame */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvQ29kZUZyYW1lL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBQVNBOzs7ZUFBQUEsV0FBQUEsU0FBUzs7O3VDQUFRIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9Db2RlRnJhbWUvaW5kZXgudHN4P2VmMzYiXSwibmFtZXMiOlsiQ29kZUZyYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/index.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/styles.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/styles.js ***!
  \************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _nooptemplate = __webpack_require__(/*! ../../helpers/noop-template */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  [data-nextjs-codeframe] {\\n    overflow: auto;\\n    border-radius: var(--size-gap-half);\\n    background-color: var(--color-ansi-bg);\\n    color: var(--color-ansi-fg);\\n  }\\n  [data-nextjs-codeframe]::selection,\\n  [data-nextjs-codeframe] *::selection {\\n    background-color: var(--color-ansi-selection);\\n  }\\n  [data-nextjs-codeframe] * {\\n    color: inherit;\\n    background-color: transparent;\\n    font-family: var(--font-stack-monospace);\\n  }\\n\\n  [data-nextjs-codeframe] > * {\\n    margin: 0;\\n    padding: calc(var(--size-gap) + var(--size-gap-half))\\n      calc(var(--size-gap-double) + var(--size-gap-half));\\n  }\\n  [data-nextjs-codeframe] > div {\\n    display: inline-block;\\n    width: auto;\\n    min-width: 100%;\\n    border-bottom: 1px solid var(--color-ansi-bright-black);\\n  }\\n  [data-nextjs-codeframe] > div > p {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    cursor: pointer;\\n    margin: 0;\\n  }\\n  [data-nextjs-codeframe] > div > p:hover {\\n    text-decoration: underline dotted;\\n  }\\n  [data-nextjs-codeframe] div > p > svg {\\n    width: auto;\\n    height: 1em;\\n    margin-left: 8px;\\n  }\\n  [data-nextjs-codeframe] div > pre {\\n    overflow: hidden;\\n    display: inline-block;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvQ29kZUZyYW1lL3N0eWxlcy5qcyIsIm1hcHBpbmdzIjoiOzs7OzBDQW1EU0E7OztlQUFBQTs7OzswQ0FuRG1COzs7Ozs7Ozs7O0FBRTVCLE1BQU1BLFNBQUFBLENBQUFBLEdBQVNDLGNBQUFBLElBQUcsRUFBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9jb21wb25lbnRzL0NvZGVGcmFtZS9zdHlsZXMudHN4P2Q3Y2QiXSwibmFtZXMiOlsic3R5bGVzIiwiY3NzIiwiX3RlbXBsYXRlT2JqZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/styles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js ***!
  \***************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CodeFrame\", ({\n    enumerable: true,\n    get: function() {\n        return CodeFrame;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _anser = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/anser */ \"(app-pages-browser)/./node_modules/next/dist/compiled/anser/index.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(app-pages-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\"));\nconst _stackframe = __webpack_require__(/*! ../../helpers/stack-frame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js\");\nconst _useopenineditor = __webpack_require__(/*! ../../helpers/use-open-in-editor */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../hot-linked-text */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/hot-linked-text/index.js\");\nconst CodeFrame = function CodeFrame(param) {\n    _s();\n    let { stackFrame, codeFrame } = param;\n    // Strip leading spaces out of the code frame:\n    const formattedFrame = _react.useMemo(()=>{\n        const lines = codeFrame.split(/\\r?\\n/g);\n        // Find the minimum length of leading spaces after `|` in the code frame\n        const miniLeadingSpacesLength = lines.map((line)=>/^>? +\\d+ +\\| [ ]+/.exec((0, _stripansi.default)(line)) === null ? null : /^>? +\\d+ +\\| ( *)/.exec((0, _stripansi.default)(line))).filter(Boolean).map((v)=>v.pop()).reduce((c, n)=>isNaN(c) ? n.length : Math.min(c, n.length), NaN);\n        // When the minimum length of leading spaces is greater than 1, remove them\n        // from the code frame to help the indentation looks better when there's a lot leading spaces.\n        if (miniLeadingSpacesLength > 1) {\n            return lines.map((line, a)=>~(a = line.indexOf(\"|\")) ? line.substring(0, a) + line.substring(a).replace(\"^\\\\ {\" + miniLeadingSpacesLength + \"}\", \"\") : line).join(\"\\n\");\n        }\n        return lines.join(\"\\n\");\n    }, [\n        codeFrame\n    ]);\n    const decoded = _react.useMemo(()=>{\n        return _anser.default.ansiToJson(formattedFrame, {\n            json: true,\n            use_classes: true,\n            remove_empty: true\n        });\n    }, [\n        formattedFrame\n    ]);\n    const open = (0, _useopenineditor.useOpenInEditor)({\n        file: stackFrame.file,\n        lineNumber: stackFrame.lineNumber,\n        column: stackFrame.column\n    });\n    // TODO: make the caret absolute\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-codeframe\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                    role: \"link\",\n                    onClick: open,\n                    tabIndex: 1,\n                    title: \"Click to open in your editor\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                            children: [\n                                (0, _stackframe.getFrameSource)(stackFrame),\n                                \" @\",\n                                \" \",\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                                    text: stackFrame.methodName\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            viewBox: \"0 0 24 24\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\",\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                                    d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"polyline\", {\n                                    points: \"15 3 21 3 21 9\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"line\", {\n                                    x1: \"10\",\n                                    y1: \"14\",\n                                    x2: \"21\",\n                                    y2: \"3\"\n                                })\n                            ]\n                        })\n                    ]\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"pre\", {\n                children: decoded.map((entry, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                        style: {\n                            color: entry.fg ? \"var(--color-\" + entry.fg + \")\" : undefined,\n                            ...entry.decoration === \"bold\" ? {\n                                fontWeight: 800\n                            } : entry.decoration === \"italic\" ? {\n                                fontStyle: \"italic\"\n                            } : undefined\n                        },\n                        children: entry.content\n                    }, \"frame-\" + index))\n            })\n        ]\n    });\n};\n_s(CodeFrame, \"4Gt+UceGaRqMOFv99GzJu8D2nx8=\");\n_c = CodeFrame;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=CodeFrame.js.map\nvar _c;\n$RefreshReg$(_c, \"CodeFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/index.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/index.js ***!
  \***********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CodeFrame\", ({\n    enumerable: true,\n    get: function() {\n        return _CodeFrame.CodeFrame;\n    }\n}));\nconst _CodeFrame = __webpack_require__(/*! ./CodeFrame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9Db2RlRnJhbWUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs2Q0FBU0E7OztlQUFBQSxXQUFBQSxTQUFTOzs7dUNBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9jb21wb25lbnRzL0NvZGVGcmFtZS9pbmRleC50c3g/ZWYzNiJdLCJuYW1lcyI6WyJDb2RlRnJhbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/styles.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/styles.js ***!
  \************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _nooptemplate = __webpack_require__(/*! ../../helpers/noop-template */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  [data-nextjs-codeframe] {\\n    overflow: auto;\\n    border-radius: var(--size-gap-half);\\n    background-color: var(--color-ansi-bg);\\n    color: var(--color-ansi-fg);\\n  }\\n  [data-nextjs-codeframe]::selection,\\n  [data-nextjs-codeframe] *::selection {\\n    background-color: var(--color-ansi-selection);\\n  }\\n  [data-nextjs-codeframe] * {\\n    color: inherit;\\n    background-color: transparent;\\n    font-family: var(--font-stack-monospace);\\n  }\\n\\n  [data-nextjs-codeframe] > * {\\n    margin: 0;\\n    padding: calc(var(--size-gap) + var(--size-gap-half))\\n      calc(var(--size-gap-double) + var(--size-gap-half));\\n  }\\n  [data-nextjs-codeframe] > div {\\n    display: inline-block;\\n    width: auto;\\n    min-width: 100%;\\n    border-bottom: 1px solid var(--color-ansi-bright-black);\\n  }\\n  [data-nextjs-codeframe] > div > p {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    cursor: pointer;\\n    margin: 0;\\n  }\\n  [data-nextjs-codeframe] > div > p:hover {\\n    text-decoration: underline dotted;\\n  }\\n  [data-nextjs-codeframe] div > p > svg {\\n    width: auto;\\n    height: 1em;\\n    margin-left: 8px;\\n  }\\n  [data-nextjs-codeframe] div > pre {\\n    overflow: hidden;\\n    display: inline-block;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9Db2RlRnJhbWUvc3R5bGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7MENBbURTQTs7O2VBQUFBOzs7OzBDQW5EbUI7Ozs7Ozs7Ozs7QUFFNUIsTUFBTUEsU0FBQUEsQ0FBQUEsR0FBU0MsY0FBQUEsSUFBRyxFQUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvQ29kZUZyYW1lL3N0eWxlcy50c3g/ZDdjZCJdLCJuYW1lcyI6WyJzdHlsZXMiLCJjc3MiLCJfdGVtcGxhdGVPYmplY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/styles.js\n"));

/***/ })

}]);