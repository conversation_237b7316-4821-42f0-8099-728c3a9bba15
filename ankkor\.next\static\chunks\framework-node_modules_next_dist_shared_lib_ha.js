"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_shared_lib_ha"],{

/***/ "./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HeadManagerContext\", ({\n    enumerable: true,\n    get: function() {\n        return HeadManagerContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst HeadManagerContext = _react.default.createContext({});\nif (true) {\n    HeadManagerContext.displayName = \"HeadManagerContext\";\n} //# sourceMappingURL=head-manager-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvaGVhZC1tYW5hZ2VyLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUuanMiLCJtYXBwaW5ncyI6Ijs7OztzREFFYUE7OztlQUFBQTs7Ozs0RUFGSztBQUVYLE1BQU1BLHFCQVVSQyxPQUFBQSxPQUFLLENBQUNDLGFBQWEsQ0FBQyxDQUFDO0FBRTFCLElBQUlDLElBQXlCLEVBQWM7SUFDekNILG1CQUFtQkksV0FBVyxHQUFHO0FBQ25DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvc2hhcmVkL2xpYi9oZWFkLW1hbmFnZXItY29udGV4dC5zaGFyZWQtcnVudGltZS50cz9jMDRjIl0sIm5hbWVzIjpbIkhlYWRNYW5hZ2VyQ29udGV4dCIsIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInByb2Nlc3MiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/head.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head.js ***!
  \***************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    defaultHead: function() {\n        return defaultHead;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _sideeffect = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./side-effect */ \"./node_modules/next/dist/shared/lib/side-effect.js\"));\nconst _ampcontextsharedruntime = __webpack_require__(/*! ./amp-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ./head-manager-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _ampmode = __webpack_require__(/*! ./amp-mode */ \"./node_modules/next/dist/shared/lib/amp-mode.js\");\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction defaultHead(inAmpMode) {\n    if (inAmpMode === void 0) inAmpMode = false;\n    const head = [\n        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            charSet: \"utf-8\"\n        })\n    ];\n    if (!inAmpMode) {\n        head.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width\"\n        }));\n    }\n    return head;\n}\nfunction onlyReactElement(list, child) {\n    // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n    if (typeof child === \"string\" || typeof child === \"number\") {\n        return list;\n    }\n    // Adds support for React.Fragment\n    if (child.type === _react.default.Fragment) {\n        return list.concat(_react.default.Children.toArray(child.props.children).reduce((fragmentList, fragmentChild)=>{\n            if (typeof fragmentChild === \"string\" || typeof fragmentChild === \"number\") {\n                return fragmentList;\n            }\n            return fragmentList.concat(fragmentChild);\n        }, []));\n    }\n    return list.concat(child);\n}\nconst METATYPES = [\n    \"name\",\n    \"httpEquiv\",\n    \"charSet\",\n    \"itemProp\"\n];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/ function unique() {\n    const keys = new Set();\n    const tags = new Set();\n    const metaTypes = new Set();\n    const metaCategories = {};\n    return (h)=>{\n        let isUnique = true;\n        let hasKey = false;\n        if (h.key && typeof h.key !== \"number\" && h.key.indexOf(\"$\") > 0) {\n            hasKey = true;\n            const key = h.key.slice(h.key.indexOf(\"$\") + 1);\n            if (keys.has(key)) {\n                isUnique = false;\n            } else {\n                keys.add(key);\n            }\n        }\n        // eslint-disable-next-line default-case\n        switch(h.type){\n            case \"title\":\n            case \"base\":\n                if (tags.has(h.type)) {\n                    isUnique = false;\n                } else {\n                    tags.add(h.type);\n                }\n                break;\n            case \"meta\":\n                for(let i = 0, len = METATYPES.length; i < len; i++){\n                    const metatype = METATYPES[i];\n                    if (!h.props.hasOwnProperty(metatype)) continue;\n                    if (metatype === \"charSet\") {\n                        if (metaTypes.has(metatype)) {\n                            isUnique = false;\n                        } else {\n                            metaTypes.add(metatype);\n                        }\n                    } else {\n                        const category = h.props[metatype];\n                        const categories = metaCategories[metatype] || new Set();\n                        if ((metatype !== \"name\" || !hasKey) && categories.has(category)) {\n                            isUnique = false;\n                        } else {\n                            categories.add(category);\n                            metaCategories[metatype] = categories;\n                        }\n                    }\n                }\n                break;\n        }\n        return isUnique;\n    };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */ function reduceComponents(headChildrenElements, props) {\n    const { inAmpMode } = props;\n    return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i)=>{\n        const key = c.key || i;\n        if (false) {}\n        if (true) {\n            // omit JSON-LD structured data snippets from the warning\n            if (c.type === \"script\" && c.props[\"type\"] !== \"application/ld+json\") {\n                const srcMessage = c.props[\"src\"] ? '<script> tag with src=\"' + c.props[\"src\"] + '\"' : \"inline <script>\";\n                (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n            } else if (c.type === \"link\" && c.props[\"rel\"] === \"stylesheet\") {\n                (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props[\"href\"] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n            }\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(c, {\n            key\n        });\n    });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */ function Head(param) {\n    let { children } = param;\n    const ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n    const headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_sideeffect.default, {\n        reduceComponentsToState: reduceComponents,\n        headManager: headManager,\n        inAmpMode: (0, _ampmode.isInAmpMode)(ampState),\n        children: children\n    });\n}\n_c = Head;\nconst _default = Head;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head.js.map\nvar _c;\n$RefreshReg$(_c, \"Head\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/head.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/hash.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/hash.js ***!
  \***************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// http://www.cse.yorku.ca/~oz/hash.html\n// More specifically, 32-bit hash via djbxor\n// (ref: https://gist.github.com/eplawless/52813b1d8ad9af510d85?permalink_comment_id=3367765#gistcomment-3367765)\n// This is due to number type differences between rust for turbopack to js number types,\n// where rust does not have easy way to repreesnt js's 53-bit float number type for the matching\n// overflow behavior. This is more `correct` in terms of having canonical hash across different runtime / implementation\n// as can gaurantee determinstic output from 32bit hash.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    djb2Hash: function() {\n        return djb2Hash;\n    },\n    hexHash: function() {\n        return hexHash;\n    }\n});\nfunction djb2Hash(str) {\n    let hash = 5381;\n    for(let i = 0; i < str.length; i++){\n        const char = str.charCodeAt(i);\n        hash = (hash << 5) + hash + char & 0xffffffff;\n    }\n    return hash >>> 0;\n}\nfunction hexHash(str) {\n    return djb2Hash(str).toString(36).slice(0, 5);\n} //# sourceMappingURL=hash.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9oYXNoLmpzIiwibWFwcGluZ3MiOiJBQUFBLHdDQUF3QztBQUN4Qyw0Q0FBNEM7QUFDNUMsaUhBQWlIO0FBQ2pILHdGQUF3RjtBQUN4RixnR0FBZ0c7QUFDaEcsd0hBQXdIO0FBQ3hILHdEQUF3RDs7Ozs7Ozs7Ozs7OztJQUN4Q0EsVUFBUTtlQUFSQTs7SUFTQUMsU0FBTztlQUFQQTs7O0FBVFQsU0FBU0QsU0FBU0UsR0FBVztJQUNsQyxJQUFJQyxPQUFPO0lBQ1gsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlGLElBQUlHLE1BQU0sRUFBRUQsSUFBSztRQUNuQyxNQUFNRSxPQUFPSixJQUFJSyxVQUFVLENBQUNIO1FBQzVCRCxPQUFPLENBQUVBLFFBQVEsS0FBS0EsT0FBT0csT0FBUTtJQUN2QztJQUNBLE9BQU9ILFNBQVM7QUFDbEI7QUFFTyxTQUFTRixRQUFRQyxHQUFXO0lBQ2pDLE9BQU9GLFNBQVNFLEtBQUtNLFFBQVEsQ0FBQyxJQUFJQyxLQUFLLENBQUMsR0FBRztBQUM3QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL3NoYXJlZC9saWIvaGFzaC50cz9iZDk4Il0sIm5hbWVzIjpbImRqYjJIYXNoIiwiaGV4SGFzaCIsInN0ciIsImhhc2giLCJpIiwibGVuZ3RoIiwiY2hhciIsImNoYXJDb2RlQXQiLCJ0b1N0cmluZyIsInNsaWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/hash.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HeadManagerContext\", ({\n    enumerable: true,\n    get: function() {\n        return HeadManagerContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst HeadManagerContext = _react.default.createContext({});\nif (true) {\n    HeadManagerContext.displayName = \"HeadManagerContext\";\n} //# sourceMappingURL=head-manager-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9oZWFkLW1hbmFnZXItY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O3NEQUVhQTs7O2VBQUFBOzs7OzRFQUZLO0FBRVgsTUFBTUEscUJBVVJDLE9BQUFBLE9BQUssQ0FBQ0MsYUFBYSxDQUFDLENBQUM7QUFFMUIsSUFBSUMsSUFBeUIsRUFBYztJQUN6Q0gsbUJBQW1CSSxXQUFXLEdBQUc7QUFDbkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9zaGFyZWQvbGliL2hlYWQtbWFuYWdlci1jb250ZXh0LnNoYXJlZC1ydW50aW1lLnRzP2MwNGMiXSwibmFtZXMiOlsiSGVhZE1hbmFnZXJDb250ZXh0IiwiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwicHJvY2VzcyIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head.js ***!
  \***************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    defaultHead: function() {\n        return defaultHead;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _sideeffect = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./side-effect */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/side-effect.js\"));\nconst _ampcontextsharedruntime = __webpack_require__(/*! ./amp-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ./head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _ampmode = __webpack_require__(/*! ./amp-mode */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\");\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction defaultHead(inAmpMode) {\n    if (inAmpMode === void 0) inAmpMode = false;\n    const head = [\n        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            charSet: \"utf-8\"\n        })\n    ];\n    if (!inAmpMode) {\n        head.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width\"\n        }));\n    }\n    return head;\n}\nfunction onlyReactElement(list, child) {\n    // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n    if (typeof child === \"string\" || typeof child === \"number\") {\n        return list;\n    }\n    // Adds support for React.Fragment\n    if (child.type === _react.default.Fragment) {\n        return list.concat(_react.default.Children.toArray(child.props.children).reduce((fragmentList, fragmentChild)=>{\n            if (typeof fragmentChild === \"string\" || typeof fragmentChild === \"number\") {\n                return fragmentList;\n            }\n            return fragmentList.concat(fragmentChild);\n        }, []));\n    }\n    return list.concat(child);\n}\nconst METATYPES = [\n    \"name\",\n    \"httpEquiv\",\n    \"charSet\",\n    \"itemProp\"\n];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/ function unique() {\n    const keys = new Set();\n    const tags = new Set();\n    const metaTypes = new Set();\n    const metaCategories = {};\n    return (h)=>{\n        let isUnique = true;\n        let hasKey = false;\n        if (h.key && typeof h.key !== \"number\" && h.key.indexOf(\"$\") > 0) {\n            hasKey = true;\n            const key = h.key.slice(h.key.indexOf(\"$\") + 1);\n            if (keys.has(key)) {\n                isUnique = false;\n            } else {\n                keys.add(key);\n            }\n        }\n        // eslint-disable-next-line default-case\n        switch(h.type){\n            case \"title\":\n            case \"base\":\n                if (tags.has(h.type)) {\n                    isUnique = false;\n                } else {\n                    tags.add(h.type);\n                }\n                break;\n            case \"meta\":\n                for(let i = 0, len = METATYPES.length; i < len; i++){\n                    const metatype = METATYPES[i];\n                    if (!h.props.hasOwnProperty(metatype)) continue;\n                    if (metatype === \"charSet\") {\n                        if (metaTypes.has(metatype)) {\n                            isUnique = false;\n                        } else {\n                            metaTypes.add(metatype);\n                        }\n                    } else {\n                        const category = h.props[metatype];\n                        const categories = metaCategories[metatype] || new Set();\n                        if ((metatype !== \"name\" || !hasKey) && categories.has(category)) {\n                            isUnique = false;\n                        } else {\n                            categories.add(category);\n                            metaCategories[metatype] = categories;\n                        }\n                    }\n                }\n                break;\n        }\n        return isUnique;\n    };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */ function reduceComponents(headChildrenElements, props) {\n    const { inAmpMode } = props;\n    return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i)=>{\n        const key = c.key || i;\n        if (false) {}\n        if (true) {\n            // omit JSON-LD structured data snippets from the warning\n            if (c.type === \"script\" && c.props[\"type\"] !== \"application/ld+json\") {\n                const srcMessage = c.props[\"src\"] ? '<script> tag with src=\"' + c.props[\"src\"] + '\"' : \"inline <script>\";\n                (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n            } else if (c.type === \"link\" && c.props[\"rel\"] === \"stylesheet\") {\n                (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props[\"href\"] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n            }\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(c, {\n            key\n        });\n    });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */ function Head(param) {\n    let { children } = param;\n    const ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n    const headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_sideeffect.default, {\n        reduceComponentsToState: reduceComponents,\n        headManager: headManager,\n        inAmpMode: (0, _ampmode.isInAmpMode)(ampState),\n        children: children\n    });\n}\n_c = Head;\nconst _default = Head;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head.js.map\nvar _c;\n$RefreshReg$(_c, \"Head\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js\n"));

/***/ })

}]);