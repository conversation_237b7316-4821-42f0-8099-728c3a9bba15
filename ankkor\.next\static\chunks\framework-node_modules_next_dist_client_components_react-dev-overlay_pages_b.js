"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_pages_b"],{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/pages/bus.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/pages/bus.js ***!
  \*********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    emit: function() {\n        return emit;\n    },\n    off: function() {\n        return off;\n    },\n    on: function() {\n        return on;\n    }\n});\nlet handlers = new Set();\nlet queue = [];\nfunction drain() {\n    // Draining should never happen synchronously in case multiple handlers are\n    // registered.\n    setTimeout(function() {\n        while(Boolean(queue.length) && // Or, if all handlers removed themselves as a result of handling the\n        // event(s)\n        Boolean(handlers.size)){\n            const ev = queue.shift();\n            handlers.forEach((handler)=>handler(ev));\n        }\n    }, 1);\n}\nfunction emit(ev) {\n    queue.push(Object.freeze({\n        ...ev\n    }));\n    drain();\n}\nfunction on(fn) {\n    if (handlers.has(fn)) {\n        return false;\n    }\n    handlers.add(fn);\n    drain();\n    return true;\n}\nfunction off(fn) {\n    if (handlers.has(fn)) {\n        handlers.delete(fn);\n        return true;\n    }\n    return false;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=bus.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L3BhZ2VzL2J1cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUF3QmdCQSxNQUFJO2VBQUpBOztJQWVBQyxLQUFHO2VBQUhBOztJQVZBQyxJQUFFO2VBQUZBOzs7QUF6QmhCLElBQUlDLFdBQWlDLElBQUlDO0FBQ3pDLElBQUlDLFFBQW9CLEVBQUU7QUFFMUIsU0FBU0M7SUFDUCwyRUFBMkU7SUFDM0UsY0FBYztJQUNkQyxXQUFXO1FBQ1QsTUFFRUMsUUFBUUgsTUFBTUksTUFBTSxLQUNwQixxRUFBcUU7UUFDckUsV0FBVztRQUNYRCxRQUFRTCxTQUFTTyxJQUFJLEVBQ3JCO1lBQ0EsTUFBTUMsS0FBS04sTUFBTU8sS0FBSztZQUN0QlQsU0FBU1UsT0FBTyxDQUFDLENBQUNDLFVBQVlBLFFBQVFIO1FBQ3hDO0lBQ0YsR0FBRztBQUNMO0FBRU8sU0FBU1gsS0FBS1csRUFBWTtJQUMvQk4sTUFBTVUsSUFBSSxDQUFDQyxPQUFPQyxNQUFNLENBQUM7UUFBRSxHQUFHTixFQUFFO0lBQUM7SUFDakNMO0FBQ0Y7QUFFTyxTQUFTSixHQUFHZ0IsRUFBbUI7SUFDcEMsSUFBSWYsU0FBU2dCLEdBQUcsQ0FBQ0QsS0FBSztRQUNwQixPQUFPO0lBQ1Q7SUFFQWYsU0FBU2lCLEdBQUcsQ0FBQ0Y7SUFDYlo7SUFDQSxPQUFPO0FBQ1Q7QUFFTyxTQUFTTCxJQUFJaUIsRUFBbUI7SUFDckMsSUFBSWYsU0FBU2dCLEdBQUcsQ0FBQ0QsS0FBSztRQUNwQmYsU0FBU2tCLE1BQU0sQ0FBQ0g7UUFDaEIsT0FBTztJQUNUO0lBRUEsT0FBTztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvcGFnZXMvYnVzLnRzP2U5MGYiXSwibmFtZXMiOlsiZW1pdCIsIm9mZiIsIm9uIiwiaGFuZGxlcnMiLCJTZXQiLCJxdWV1ZSIsImRyYWluIiwic2V0VGltZW91dCIsIkJvb2xlYW4iLCJsZW5ndGgiLCJzaXplIiwiZXYiLCJzaGlmdCIsImZvckVhY2giLCJoYW5kbGVyIiwicHVzaCIsIk9iamVjdCIsImZyZWV6ZSIsImZuIiwiaGFzIiwiYWRkIiwiZGVsZXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/pages/bus.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/pages/client.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/pages/client.js ***!
  \************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ReactDevOverlay: function() {\n        return _ReactDevOverlay.default;\n    },\n    getErrorByType: function() {\n        return _getErrorByType.getErrorByType;\n    },\n    getServerError: function() {\n        return _nodeStackFrames.getServerError;\n    },\n    onBeforeRefresh: function() {\n        return onBeforeRefresh;\n    },\n    onBuildError: function() {\n        return onBuildError;\n    },\n    onBuildOk: function() {\n        return onBuildOk;\n    },\n    onRefresh: function() {\n        return onRefresh;\n    },\n    onVersionInfo: function() {\n        return onVersionInfo;\n    },\n    register: function() {\n        return register;\n    },\n    unregister: function() {\n        return unregister;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _bus = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./bus */ \"./node_modules/next/dist/client/components/react-dev-overlay/pages/bus.js\"));\nconst _parseStack = __webpack_require__(/*! ../internal/helpers/parseStack */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parseStack.js\");\nconst _parsecomponentstack = __webpack_require__(/*! ../internal/helpers/parse-component-stack */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parse-component-stack.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ../internal/helpers/hydration-error-info */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/hydration-error-info.js\");\nconst _shared = __webpack_require__(/*! ../shared */ \"./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _getErrorByType = __webpack_require__(/*! ../internal/helpers/getErrorByType */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js\");\nconst _nodeStackFrames = __webpack_require__(/*! ../internal/helpers/nodeStackFrames */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/nodeStackFrames.js\");\nconst _ReactDevOverlay = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./ReactDevOverlay */ \"./node_modules/next/dist/client/components/react-dev-overlay/pages/ReactDevOverlay.js\"));\n// Patch console.error to collect information about hydration errors\n(0, _hydrationerrorinfo.patchConsoleError)();\nlet isRegistered = false;\nlet stackTraceLimit = undefined;\nfunction onUnhandledError(ev) {\n    const error = ev == null ? void 0 : ev.error;\n    if (!error || !(error instanceof Error) || typeof error.stack !== \"string\") {\n        // A non-error was thrown, we don't have anything to show. :-(\n        return;\n    }\n    if (error.message.match(/(hydration|content does not match|did not match)/i)) {\n        if (_hydrationerrorinfo.hydrationErrorState.warning) {\n            error.details = {\n                ...error.details,\n                // It contains the warning, component stack, server and client tag names\n                ..._hydrationerrorinfo.hydrationErrorState\n            };\n        }\n        error.message += \"\\nSee more info here: https://nextjs.org/docs/messages/react-hydration-error\";\n    }\n    const e = error;\n    const componentStackFrames = typeof _hydrationerrorinfo.hydrationErrorState.componentStack === \"string\" ? (0, _parsecomponentstack.parseComponentStack)(_hydrationerrorinfo.hydrationErrorState.componentStack) : undefined;\n    // Skip ModuleBuildError and ModuleNotFoundError, as it will be sent through onBuildError callback.\n    // This is to avoid same error as different type showing up on client to cause flashing.\n    if (e.name !== \"ModuleBuildError\" && e.name !== \"ModuleNotFoundError\") {\n        _bus.emit({\n            type: _shared.ACTION_UNHANDLED_ERROR,\n            reason: error,\n            frames: (0, _parseStack.parseStack)(e.stack),\n            componentStackFrames\n        });\n    }\n}\nfunction onUnhandledRejection(ev) {\n    const reason = ev == null ? void 0 : ev.reason;\n    if (!reason || !(reason instanceof Error) || typeof reason.stack !== \"string\") {\n        // A non-error was thrown, we don't have anything to show. :-(\n        return;\n    }\n    const e = reason;\n    _bus.emit({\n        type: _shared.ACTION_UNHANDLED_REJECTION,\n        reason: reason,\n        frames: (0, _parseStack.parseStack)(e.stack)\n    });\n}\nfunction register() {\n    if (isRegistered) {\n        return;\n    }\n    isRegistered = true;\n    try {\n        const limit = Error.stackTraceLimit;\n        Error.stackTraceLimit = 50;\n        stackTraceLimit = limit;\n    } catch (e) {}\n    window.addEventListener(\"error\", onUnhandledError);\n    window.addEventListener(\"unhandledrejection\", onUnhandledRejection);\n}\nfunction unregister() {\n    if (!isRegistered) {\n        return;\n    }\n    isRegistered = false;\n    if (stackTraceLimit !== undefined) {\n        try {\n            Error.stackTraceLimit = stackTraceLimit;\n        } catch (e) {}\n        stackTraceLimit = undefined;\n    }\n    window.removeEventListener(\"error\", onUnhandledError);\n    window.removeEventListener(\"unhandledrejection\", onUnhandledRejection);\n}\nfunction onBuildOk() {\n    _bus.emit({\n        type: _shared.ACTION_BUILD_OK\n    });\n}\nfunction onBuildError(message) {\n    _bus.emit({\n        type: _shared.ACTION_BUILD_ERROR,\n        message\n    });\n}\nfunction onRefresh() {\n    _bus.emit({\n        type: _shared.ACTION_REFRESH\n    });\n}\nfunction onBeforeRefresh() {\n    _bus.emit({\n        type: _shared.ACTION_BEFORE_REFRESH\n    });\n}\nfunction onVersionInfo(versionInfo) {\n    _bus.emit({\n        type: _shared.ACTION_VERSION_INFO,\n        versionInfo\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/pages/client.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/pages/hot-reloader-client.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/pages/hot-reloader-client.js ***!
  \*************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// TODO: Remove use of `any` type. Fix no-use-before-define violations.\n/* eslint-disable @typescript-eslint/no-use-before-define */ /**\n * MIT License\n *\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */ // This file is a modified version of the Create React App HMR dev client that\n// can be found here:\n// https://github.com/facebook/create-react-app/blob/v3.4.1/packages/react-dev-utils/webpackHotDevClient.js\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return connect;\n    },\n    performFullReload: function() {\n        return performFullReload;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _client = __webpack_require__(/*! ./client */ \"./node_modules/next/dist/client/components/react-dev-overlay/pages/client.js\");\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"./node_modules/next/dist/compiled/strip-ansi/index.js\"));\nconst _websocket = __webpack_require__(/*! ./websocket */ \"./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\");\nconst _formatwebpackmessages = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../internal/helpers/format-webpack-messages */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/format-webpack-messages.js\"));\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _extractmodulesfromturbopackmessage = __webpack_require__(/*! ../../../../server/dev/extract-modules-from-turbopack-message */ \"./node_modules/next/dist/server/dev/extract-modules-from-turbopack-message.js\");\nconst _shared = __webpack_require__(/*! ../shared */ \"./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _runtimeerrorhandler = __webpack_require__(/*! ../internal/helpers/runtime-error-handler */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/runtime-error-handler.js\");\nwindow.__nextDevClientId = Math.round(Math.random() * 100 + Date.now());\nlet customHmrEventHandler;\nlet turbopackMessageListeners = [];\nlet MODE = \"webpack\";\nfunction connect(mode) {\n    MODE = mode;\n    (0, _client.register)();\n    (0, _websocket.addMessageListener)((payload)=>{\n        if (!(\"action\" in payload)) {\n            return;\n        }\n        try {\n            processMessage(payload);\n        } catch (err) {\n            var _err_stack;\n            console.warn(\"[HMR] Invalid message: \" + payload + \"\\n\" + ((_err_stack = err == null ? void 0 : err.stack) != null ? _err_stack : \"\"));\n        }\n    });\n    return {\n        subscribeToHmrEvent (handler) {\n            customHmrEventHandler = handler;\n        },\n        onUnrecoverableError () {\n            _runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError = true;\n        },\n        addTurbopackMessageListener (cb) {\n            turbopackMessageListeners.push(cb);\n        },\n        sendTurbopackMessage (msg) {\n            (0, _websocket.sendMessage)(msg);\n        },\n        handleUpdateError (err) {\n            performFullReload(err);\n        }\n    };\n}\n// Remember some state related to hot module replacement.\nvar isFirstCompilation = true;\nvar mostRecentCompilationHash = null;\nvar hasCompileErrors = false;\nfunction clearOutdatedErrors() {\n    // Clean up outdated compile errors, if any.\n    if (typeof console !== \"undefined\" && typeof console.clear === \"function\") {\n        if (hasCompileErrors) {\n            console.clear();\n        }\n    }\n}\n// Successful compilation.\nfunction handleSuccess() {\n    clearOutdatedErrors();\n    if (MODE === \"webpack\") {\n        const isHotUpdate = !isFirstCompilation || window.__NEXT_DATA__.page !== \"/_error\" && isUpdateAvailable();\n        isFirstCompilation = false;\n        hasCompileErrors = false;\n        // Attempt to apply hot updates or reload.\n        if (isHotUpdate) {\n            tryApplyUpdates(onBeforeFastRefresh, onFastRefresh);\n        }\n    } else {\n        (0, _client.onBuildOk)();\n    }\n}\n// Compilation with warnings (e.g. ESLint).\nfunction handleWarnings(warnings) {\n    clearOutdatedErrors();\n    const isHotUpdate = !isFirstCompilation;\n    isFirstCompilation = false;\n    hasCompileErrors = false;\n    function printWarnings() {\n        // Print warnings to the console.\n        const formatted = (0, _formatwebpackmessages.default)({\n            warnings: warnings,\n            errors: []\n        });\n        if (typeof console !== \"undefined\" && typeof console.warn === \"function\") {\n            var _formatted_warnings;\n            for(let i = 0; i < ((_formatted_warnings = formatted.warnings) == null ? void 0 : _formatted_warnings.length); i++){\n                if (i === 5) {\n                    console.warn(\"There were more warnings in other files.\\n\" + \"You can find a complete log in the terminal.\");\n                    break;\n                }\n                console.warn((0, _stripansi.default)(formatted.warnings[i]));\n            }\n        }\n    }\n    printWarnings();\n    // Attempt to apply hot updates or reload.\n    if (isHotUpdate) {\n        tryApplyUpdates(onBeforeFastRefresh, onFastRefresh);\n    }\n}\n// Compilation with errors (e.g. syntax error or missing modules).\nfunction handleErrors(errors) {\n    clearOutdatedErrors();\n    isFirstCompilation = false;\n    hasCompileErrors = true;\n    // \"Massage\" webpack messages.\n    var formatted = (0, _formatwebpackmessages.default)({\n        errors: errors,\n        warnings: []\n    });\n    // Only show the first error.\n    (0, _client.onBuildError)(formatted.errors[0]);\n    // Also log them to the console.\n    if (typeof console !== \"undefined\" && typeof console.error === \"function\") {\n        for(var i = 0; i < formatted.errors.length; i++){\n            console.error((0, _stripansi.default)(formatted.errors[i]));\n        }\n    }\n    // Do not attempt to reload now.\n    // We will reload on next success instead.\n    if (false) {}\n}\nlet startLatency = undefined;\nfunction onBeforeFastRefresh(updatedModules) {\n    if (updatedModules.length > 0) {\n        // Only trigger a pending state if we have updates to apply\n        // (cf. onFastRefresh)\n        (0, _client.onBeforeRefresh)();\n    }\n}\nfunction onFastRefresh(updatedModules) {\n    if (updatedModules === void 0) updatedModules = [];\n    (0, _client.onBuildOk)();\n    if (updatedModules.length === 0) {\n        return;\n    }\n    (0, _client.onRefresh)();\n    reportHmrLatency();\n}\nfunction reportHmrLatency(updatedModules) {\n    if (updatedModules === void 0) updatedModules = [];\n    if (startLatency) {\n        const endLatency = Date.now();\n        const latency = endLatency - startLatency;\n        console.log(\"[Fast Refresh] done in \" + latency + \"ms\");\n        (0, _websocket.sendMessage)(JSON.stringify({\n            event: \"client-hmr-latency\",\n            id: window.__nextDevClientId,\n            startTime: startLatency,\n            endTime: endLatency,\n            page: window.location.pathname,\n            updatedModules,\n            // Whether the page (tab) was hidden at the time the event occurred.\n            // This can impact the accuracy of the event's timing.\n            isPageHidden: document.visibilityState === \"hidden\"\n        }));\n        if (self.__NEXT_HMR_LATENCY_CB) {\n            self.__NEXT_HMR_LATENCY_CB(latency);\n        }\n    }\n}\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash) {\n    // Update last known compilation hash.\n    mostRecentCompilationHash = hash;\n}\n/** Handles messages from the sevrer for the Pages Router. */ function processMessage(obj) {\n    if (!(\"action\" in obj)) {\n        return;\n    }\n    // Use turbopack message for analytics, (still need built for webpack)\n    switch(obj.action){\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n            {\n                startLatency = Date.now();\n                console.log(\"[Fast Refresh] rebuilding\");\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n            {\n                if (obj.hash) handleAvailableHash(obj.hash);\n                const { errors, warnings } = obj;\n                // Is undefined when it's a 'built' event\n                if (\"versionInfo\" in obj) (0, _client.onVersionInfo)(obj.versionInfo);\n                const hasErrors = Boolean(errors && errors.length);\n                if (hasErrors) {\n                    (0, _websocket.sendMessage)(JSON.stringify({\n                        event: \"client-error\",\n                        errorCount: errors.length,\n                        clientId: window.__nextDevClientId\n                    }));\n                    return handleErrors(errors);\n                }\n                const hasWarnings = Boolean(warnings && warnings.length);\n                if (hasWarnings) {\n                    (0, _websocket.sendMessage)(JSON.stringify({\n                        event: \"client-warning\",\n                        warningCount: warnings.length,\n                        clientId: window.__nextDevClientId\n                    }));\n                    return handleWarnings(warnings);\n                }\n                (0, _websocket.sendMessage)(JSON.stringify({\n                    event: \"client-success\",\n                    clientId: window.__nextDevClientId\n                }));\n                return handleSuccess();\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES:\n            {\n                window.location.reload();\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR:\n            {\n                const { errorJSON } = obj;\n                if (errorJSON) {\n                    const { message, stack } = JSON.parse(errorJSON);\n                    const error = new Error(message);\n                    error.stack = stack;\n                    handleErrors([\n                        error\n                    ]);\n                }\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED:\n            {\n                for (const listener of turbopackMessageListeners){\n                    listener({\n                        type: _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n                    });\n                }\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE:\n            {\n                const updatedModules = (0, _extractmodulesfromturbopackmessage.extractModulesFromTurbopackMessage)(obj.data);\n                onBeforeFastRefresh(updatedModules);\n                for (const listener of turbopackMessageListeners){\n                    listener({\n                        type: _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n                        data: obj.data\n                    });\n                }\n                if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                    console.warn(_shared.REACT_REFRESH_FULL_RELOAD_FROM_ERROR);\n                    performFullReload(null);\n                }\n                (0, _client.onRefresh)();\n                reportHmrLatency(updatedModules);\n                break;\n            }\n        default:\n            {\n                if (customHmrEventHandler) {\n                    customHmrEventHandler(obj);\n                    break;\n                }\n                break;\n            }\n    }\n}\n// Is there a newer version of this code available?\nfunction isUpdateAvailable() {\n    /* globals __webpack_hash__ */ // __webpack_hash__ is the hash of the current compilation.\n    // It's a global variable injected by Webpack.\n    return mostRecentCompilationHash !== __webpack_require__.h();\n}\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n    // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n    return module.hot.status() === \"idle\";\n}\nfunction afterApplyUpdates(fn) {\n    if (canApplyUpdates()) {\n        fn();\n    } else {\n        function handler(status) {\n            if (status === \"idle\") {\n                // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n                module.hot.removeStatusHandler(handler);\n                fn();\n            }\n        }\n        // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n        module.hot.addStatusHandler(handler);\n    }\n}\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdates(onBeforeHotUpdate, onHotUpdateSuccess) {\n    // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n    if (false) {}\n    if (!isUpdateAvailable() || !canApplyUpdates()) {\n        (0, _client.onBuildOk)();\n        return;\n    }\n    function handleApplyUpdates(err, updatedModules) {\n        if (err || _runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError || !updatedModules) {\n            if (err) {\n                console.warn(\"[Fast Refresh] performing full reload\\n\\n\" + \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" + \"You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n\" + \"Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n\" + \"It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n\" + \"Fast Refresh requires at least one parent function component in your React tree.\");\n            } else if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                console.warn(\"[Fast Refresh] performing full reload because your application had an unrecoverable error\");\n            }\n            performFullReload(err);\n            return;\n        }\n        if (typeof onHotUpdateSuccess === \"function\") {\n            // Maybe we want to do something.\n            onHotUpdateSuccess(updatedModules);\n        }\n        if (isUpdateAvailable()) {\n            // While we were updating, there was a new update! Do it again.\n            // However, this time, don't trigger a pending refresh state.\n            tryApplyUpdates(updatedModules.length > 0 ? undefined : onBeforeHotUpdate, updatedModules.length > 0 ? _client.onBuildOk : onHotUpdateSuccess);\n        } else {\n            (0, _client.onBuildOk)();\n            if (false) {}\n        }\n    }\n    // https://webpack.js.org/api/hot-module-replacement/#check\n    // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n    module.hot.check(/* autoApply */ false).then((updatedModules)=>{\n        if (!updatedModules) {\n            return null;\n        }\n        if (typeof onBeforeHotUpdate === \"function\") {\n            onBeforeHotUpdate(updatedModules);\n        }\n        // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n        return module.hot.apply();\n    }).then((updatedModules)=>{\n        handleApplyUpdates(null, updatedModules);\n    }, (err)=>{\n        handleApplyUpdates(err, null);\n    });\n}\nfunction performFullReload(err) {\n    const stackTrace = err && (err.stack && err.stack.split(\"\\n\").slice(0, 5).join(\"\\n\") || err.message || err + \"\");\n    (0, _websocket.sendMessage)(JSON.stringify({\n        event: \"client-full-reload\",\n        stackTrace,\n        hadRuntimeError: !!_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError,\n        dependencyChain: err ? err.dependencyChain : undefined\n    }));\n    window.location.reload();\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hot-reloader-client.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/pages/hot-reloader-client.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js ***!
  \***************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    addMessageListener: function() {\n        return addMessageListener;\n    },\n    connectHMR: function() {\n        return connectHMR;\n    },\n    sendMessage: function() {\n        return sendMessage;\n    }\n});\nconst _getsocketurl = __webpack_require__(/*! ../internal/helpers/get-socket-url */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/get-socket-url.js\");\nlet source;\nconst eventCallbacks = [];\nfunction addMessageListener(callback) {\n    eventCallbacks.push(callback);\n}\nfunction sendMessage(data) {\n    if (!source || source.readyState !== source.OPEN) return;\n    return source.send(data);\n}\nlet reconnections = 0;\nfunction connectHMR(options) {\n    function init() {\n        if (source) source.close();\n        function handleOnline() {\n            reconnections = 0;\n            window.console.log(\"[HMR] connected\");\n        }\n        function handleMessage(event) {\n            // Coerce into HMR_ACTION_TYPES as that is the format.\n            const msg = JSON.parse(event.data);\n            for (const eventCallback of eventCallbacks){\n                eventCallback(msg);\n            }\n        }\n        let timer;\n        function handleDisconnect() {\n            source.onerror = null;\n            source.onclose = null;\n            source.close();\n            reconnections++;\n            // After 25 reconnects we'll want to reload the page as it indicates the dev server is no longer running.\n            if (reconnections > 25) {\n                window.location.reload();\n                return;\n            }\n            clearTimeout(timer);\n            // Try again after 5 seconds\n            timer = setTimeout(init, reconnections > 5 ? 5000 : 1000);\n        }\n        const url = (0, _getsocketurl.getSocketUrl)(options.assetPrefix);\n        source = new window.WebSocket(\"\" + url + options.path);\n        source.onopen = handleOnline;\n        source.onerror = handleDisconnect;\n        source.onclose = handleDisconnect;\n        source.onmessage = handleMessage;\n    }\n    init();\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=websocket.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\n"));

/***/ })

}]);