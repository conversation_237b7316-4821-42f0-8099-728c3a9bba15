"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_s"],{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js ***!
  \****************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getFrameSource: function() {\n        return getFrameSource;\n    },\n    getOriginalStackFrames: function() {\n        return getOriginalStackFrames;\n    }\n});\nfunction getOriginalStackFrame(source, type, isAppDir, errorMessage) {\n    var _source_file, _source_file1;\n    async function _getOriginalStackFrame() {\n        var /* collapsed */ _source_file, _body_originalStackFrame_file, _body_originalStackFrame, _body_originalStackFrame_file1, _body_originalStackFrame1;\n        const params = new URLSearchParams();\n        params.append(\"isServer\", String(type === \"server\"));\n        params.append(\"isEdgeServer\", String(type === \"edge-server\"));\n        params.append(\"isAppDirectory\", String(isAppDir));\n        params.append(\"errorMessage\", errorMessage);\n        for(const key in source){\n            var _source_key;\n            params.append(key, ((_source_key = source[key]) != null ? _source_key : \"\").toString());\n        }\n        const controller = new AbortController();\n        const tm = setTimeout(()=>controller.abort(), 3000);\n        const res = await self.fetch(( false || \"\") + \"/__nextjs_original-stack-frame?\" + params.toString(), {\n            signal: controller.signal\n        }).finally(()=>{\n            clearTimeout(tm);\n        });\n        if (!res.ok || res.status === 204) {\n            return Promise.reject(new Error(await res.text()));\n        }\n        const body = await res.json();\n        var _ref;\n        return {\n            error: false,\n            reason: null,\n            external: false,\n            expanded: !Boolean((_ref = ((_source_file = source.file) == null ? void 0 : _source_file.includes(\"node_modules\")) || ((_body_originalStackFrame = body.originalStackFrame) == null ? void 0 : (_body_originalStackFrame_file = _body_originalStackFrame.file) == null ? void 0 : _body_originalStackFrame_file.includes(\"node_modules\")) || ((_body_originalStackFrame1 = body.originalStackFrame) == null ? void 0 : (_body_originalStackFrame_file1 = _body_originalStackFrame1.file) == null ? void 0 : _body_originalStackFrame_file1.startsWith(\"[turbopack]/\"))) != null ? _ref : true),\n            sourceStackFrame: source,\n            originalStackFrame: body.originalStackFrame,\n            originalCodeFrame: body.originalCodeFrame || null,\n            sourcePackage: body.sourcePackage\n        };\n    }\n    if (source.file === \"<anonymous>\" || ((_source_file = source.file) == null ? void 0 : _source_file.match(/^node:/)) || ((_source_file1 = source.file) == null ? void 0 : _source_file1.match(/https?:\\/\\//))) {\n        return Promise.resolve({\n            error: false,\n            reason: null,\n            external: true,\n            expanded: false,\n            sourceStackFrame: source,\n            originalStackFrame: null,\n            originalCodeFrame: null,\n            sourcePackage: null\n        });\n    }\n    return _getOriginalStackFrame().catch((err)=>{\n        var _err_message, _ref;\n        return {\n            error: true,\n            reason: (_ref = (_err_message = err == null ? void 0 : err.message) != null ? _err_message : err == null ? void 0 : err.toString()) != null ? _ref : \"Unknown Error\",\n            external: false,\n            expanded: false,\n            sourceStackFrame: source,\n            originalStackFrame: null,\n            originalCodeFrame: null,\n            sourcePackage: null\n        };\n    });\n}\nfunction getOriginalStackFrames(frames, type, isAppDir, errorMessage) {\n    return Promise.all(frames.map((frame)=>getOriginalStackFrame(frame, type, isAppDir, errorMessage)));\n}\nconst webpackRegExes = [\n    /^webpack-internal:\\/\\/\\/(\\.)?(\\((\\w+)\\))?/,\n    /^(webpack:\\/\\/\\/(\\.)?|webpack:\\/\\/(_N_E\\/)?)(\\((\\w+)\\))?/\n];\nfunction isWebpackBundled(file) {\n    return webpackRegExes.some((regEx)=>regEx.test(file));\n}\n/**\n * Format the webpack internal id to original file path\n * webpack-internal:///./src/hello.tsx => ./src/hello.tsx\n * webpack://_N_E/./src/hello.tsx => ./src/hello.tsx\n * webpack://./src/hello.tsx => ./src/hello.tsx\n * webpack:///./src/hello.tsx => ./src/hello.tsx\n */ function formatFrameSourceFile(file) {\n    for (const regex of webpackRegExes)file = file.replace(regex, \"\");\n    return file;\n}\nfunction getFrameSource(frame) {\n    if (!frame.file) return \"\";\n    let str = \"\";\n    try {\n        var _globalThis_location;\n        const u = new URL(frame.file);\n        // Strip the origin for same-origin scripts.\n        if (((_globalThis_location = globalThis.location) == null ? void 0 : _globalThis_location.origin) !== u.origin) {\n            // URLs can be valid without an `origin`, so long as they have a\n            // `protocol`. However, `origin` is preferred.\n            if (u.origin === \"null\") {\n                str += u.protocol;\n            } else {\n                str += u.origin;\n            }\n        }\n        // Strip query string information as it's typically too verbose to be\n        // meaningful.\n        str += u.pathname;\n        str += \" \";\n        str = formatFrameSourceFile(str);\n    } catch (e) {\n        str += formatFrameSourceFile(frame.file || \"(unknown)\") + \" \";\n    }\n    if (!isWebpackBundled(frame.file) && frame.lineNumber != null) {\n        if (frame.column != null) {\n            str += \"(\" + frame.lineNumber + \":\" + frame.column + \") \";\n        } else {\n            str += \"(\" + frame.lineNumber + \") \";\n        }\n    }\n    return str.slice(0, -1);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=stack-frame.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.js ***!
  \***********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useOpenInEditor\", ({\n    enumerable: true,\n    get: function() {\n        return useOpenInEditor;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nfunction useOpenInEditor(param) {\n    let { file, lineNumber, column } = param === void 0 ? {} : param;\n    const openInEditor = (0, _react.useCallback)(()=>{\n        if (file == null || lineNumber == null || column == null) return;\n        const params = new URLSearchParams();\n        params.append(\"file\", file);\n        params.append(\"lineNumber\", String(lineNumber));\n        params.append(\"column\", String(column));\n        self.fetch(( false || \"\") + \"/__nextjs_launch-editor?\" + params.toString()).then(()=>{}, ()=>{\n            console.error(\"There was an issue opening this code in your editor.\");\n        });\n    }, [\n        file,\n        lineNumber,\n        column\n    ]);\n    return openInEditor;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-open-in-editor.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvdXNlLW9wZW4taW4tZWRpdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7bURBRWdCQTs7O2VBQUFBOzs7bUNBRlk7QUFFckIsU0FBU0EsZ0JBQWdCQyxLQUFBO0lBQUEsTUFDOUJDLElBQUksRUFDSkMsVUFBVSxFQUNWQyxNQUFNLEVBS1AsR0FSK0JILFVBQUEsU0FRNUIsQ0FBQyxJQVIyQkE7SUFTOUIsTUFBTUksZUFBZUMsQ0FBQUEsR0FBQUEsT0FBQUEsV0FBVyxFQUFDO1FBQy9CLElBQUlKLFFBQVEsUUFBUUMsY0FBYyxRQUFRQyxVQUFVLE1BQU07UUFFMUQsTUFBTUcsU0FBUyxJQUFJQztRQUNuQkQsT0FBT0UsTUFBTSxDQUFDLFFBQVFQO1FBQ3RCSyxPQUFPRSxNQUFNLENBQUMsY0FBY0MsT0FBT1A7UUFDbkNJLE9BQU9FLE1BQU0sQ0FBQyxVQUFVQyxPQUFPTjtRQUUvQk8sS0FDR0MsS0FBSyxDQUNKLENBQ0VDLE1BQWtDLElBQUksTUFDdkMsNkJBQTBCTixPQUFPUyxRQUFRLElBRTNDQyxJQUFJLENBQ0gsS0FBTyxHQUNQO1lBQ0VDLFFBQVFDLEtBQUssQ0FBQztRQUNoQjtJQUVOLEdBQUc7UUFBQ2pCO1FBQU1DO1FBQVlDO0tBQU87SUFFN0IsT0FBT0M7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvdXNlLW9wZW4taW4tZWRpdG9yLnRzPzA4ODYiXSwibmFtZXMiOlsidXNlT3BlbkluRWRpdG9yIiwicGFyYW0iLCJmaWxlIiwibGluZU51bWJlciIsImNvbHVtbiIsIm9wZW5JbkVkaXRvciIsInVzZUNhbGxiYWNrIiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwiYXBwZW5kIiwiU3RyaW5nIiwic2VsZiIsImZldGNoIiwicHJvY2VzcyIsImVudiIsIl9fTkVYVF9ST1VURVJfQkFTRVBBVEgiLCJ0b1N0cmluZyIsInRoZW4iLCJjb25zb2xlIiwiZXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js ***!
  \****************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getFrameSource: function() {\n        return getFrameSource;\n    },\n    getOriginalStackFrames: function() {\n        return getOriginalStackFrames;\n    }\n});\nfunction getOriginalStackFrame(source, type, isAppDir, errorMessage) {\n    var _source_file, _source_file1;\n    async function _getOriginalStackFrame() {\n        var /* collapsed */ _source_file, _body_originalStackFrame_file, _body_originalStackFrame, _body_originalStackFrame_file1, _body_originalStackFrame1;\n        const params = new URLSearchParams();\n        params.append(\"isServer\", String(type === \"server\"));\n        params.append(\"isEdgeServer\", String(type === \"edge-server\"));\n        params.append(\"isAppDirectory\", String(isAppDir));\n        params.append(\"errorMessage\", errorMessage);\n        for(const key in source){\n            var _source_key;\n            params.append(key, ((_source_key = source[key]) != null ? _source_key : \"\").toString());\n        }\n        const controller = new AbortController();\n        const tm = setTimeout(()=>controller.abort(), 3000);\n        const res = await self.fetch(( false || \"\") + \"/__nextjs_original-stack-frame?\" + params.toString(), {\n            signal: controller.signal\n        }).finally(()=>{\n            clearTimeout(tm);\n        });\n        if (!res.ok || res.status === 204) {\n            return Promise.reject(new Error(await res.text()));\n        }\n        const body = await res.json();\n        var _ref;\n        return {\n            error: false,\n            reason: null,\n            external: false,\n            expanded: !Boolean((_ref = ((_source_file = source.file) == null ? void 0 : _source_file.includes(\"node_modules\")) || ((_body_originalStackFrame = body.originalStackFrame) == null ? void 0 : (_body_originalStackFrame_file = _body_originalStackFrame.file) == null ? void 0 : _body_originalStackFrame_file.includes(\"node_modules\")) || ((_body_originalStackFrame1 = body.originalStackFrame) == null ? void 0 : (_body_originalStackFrame_file1 = _body_originalStackFrame1.file) == null ? void 0 : _body_originalStackFrame_file1.startsWith(\"[turbopack]/\"))) != null ? _ref : true),\n            sourceStackFrame: source,\n            originalStackFrame: body.originalStackFrame,\n            originalCodeFrame: body.originalCodeFrame || null,\n            sourcePackage: body.sourcePackage\n        };\n    }\n    if (source.file === \"<anonymous>\" || ((_source_file = source.file) == null ? void 0 : _source_file.match(/^node:/)) || ((_source_file1 = source.file) == null ? void 0 : _source_file1.match(/https?:\\/\\//))) {\n        return Promise.resolve({\n            error: false,\n            reason: null,\n            external: true,\n            expanded: false,\n            sourceStackFrame: source,\n            originalStackFrame: null,\n            originalCodeFrame: null,\n            sourcePackage: null\n        });\n    }\n    return _getOriginalStackFrame().catch((err)=>{\n        var _err_message, _ref;\n        return {\n            error: true,\n            reason: (_ref = (_err_message = err == null ? void 0 : err.message) != null ? _err_message : err == null ? void 0 : err.toString()) != null ? _ref : \"Unknown Error\",\n            external: false,\n            expanded: false,\n            sourceStackFrame: source,\n            originalStackFrame: null,\n            originalCodeFrame: null,\n            sourcePackage: null\n        };\n    });\n}\nfunction getOriginalStackFrames(frames, type, isAppDir, errorMessage) {\n    return Promise.all(frames.map((frame)=>getOriginalStackFrame(frame, type, isAppDir, errorMessage)));\n}\nconst webpackRegExes = [\n    /^webpack-internal:\\/\\/\\/(\\.)?(\\((\\w+)\\))?/,\n    /^(webpack:\\/\\/\\/(\\.)?|webpack:\\/\\/(_N_E\\/)?)(\\((\\w+)\\))?/\n];\nfunction isWebpackBundled(file) {\n    return webpackRegExes.some((regEx)=>regEx.test(file));\n}\n/**\n * Format the webpack internal id to original file path\n * webpack-internal:///./src/hello.tsx => ./src/hello.tsx\n * webpack://_N_E/./src/hello.tsx => ./src/hello.tsx\n * webpack://./src/hello.tsx => ./src/hello.tsx\n * webpack:///./src/hello.tsx => ./src/hello.tsx\n */ function formatFrameSourceFile(file) {\n    for (const regex of webpackRegExes)file = file.replace(regex, \"\");\n    return file;\n}\nfunction getFrameSource(frame) {\n    if (!frame.file) return \"\";\n    let str = \"\";\n    try {\n        var _globalThis_location;\n        const u = new URL(frame.file);\n        // Strip the origin for same-origin scripts.\n        if (((_globalThis_location = globalThis.location) == null ? void 0 : _globalThis_location.origin) !== u.origin) {\n            // URLs can be valid without an `origin`, so long as they have a\n            // `protocol`. However, `origin` is preferred.\n            if (u.origin === \"null\") {\n                str += u.protocol;\n            } else {\n                str += u.origin;\n            }\n        }\n        // Strip query string information as it's typically too verbose to be\n        // meaningful.\n        str += u.pathname;\n        str += \" \";\n        str = formatFrameSourceFile(str);\n    } catch (e) {\n        str += formatFrameSourceFile(frame.file || \"(unknown)\") + \" \";\n    }\n    if (!isWebpackBundled(frame.file) && frame.lineNumber != null) {\n        if (frame.column != null) {\n            str += \"(\" + frame.lineNumber + \":\" + frame.column + \") \";\n        } else {\n            str += \"(\" + frame.lineNumber + \") \";\n        }\n    }\n    return str.slice(0, -1);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=stack-frame.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9zdGFjay1mcmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUE2SGdCQSxnQkFBYztlQUFkQTs7SUFsQ0FDLHdCQUFzQjtlQUF0QkE7OztBQWhGaEIsU0FBU0Msc0JBQ1BDLE1BQWtCLEVBQ2xCQyxJQUFxQyxFQUNyQ0MsUUFBaUIsRUFDakJDLFlBQW9CO1FBaURsQkgsY0FDQUE7SUFoREYsZUFBZUk7WUFnQ1QsYUFBYSxHQUNaSixjQUNDSywrQkFBQUEsMEJBQ0FBLGdDQUFBQTtRQWxDTixNQUFNQyxTQUFTLElBQUlDO1FBQ25CRCxPQUFPRSxNQUFNLENBQUMsWUFBWUMsT0FBT1IsU0FBUztRQUMxQ0ssT0FBT0UsTUFBTSxDQUFDLGdCQUFnQkMsT0FBT1IsU0FBUztRQUM5Q0ssT0FBT0UsTUFBTSxDQUFDLGtCQUFrQkMsT0FBT1A7UUFDdkNJLE9BQU9FLE1BQU0sQ0FBQyxnQkFBZ0JMO1FBQzlCLElBQUssTUFBTU8sT0FBT1YsT0FBUTtnQkFDSlc7WUFBcEJMLE9BQU9FLE1BQU0sQ0FBQ0UsS0FBSyxDQUFDLENBQUFDLGNBQUFYLE1BQWUsQ0FBQ1UsSUFBSSxZQUFwQkMsY0FBd0IsSUFBSUMsUUFBUTtRQUMxRDtRQUVBLE1BQU1DLGFBQWEsSUFBSUM7UUFDdkIsTUFBTUMsS0FBS0MsV0FBVyxJQUFNSCxXQUFXSSxLQUFLLElBQUk7UUFDaEQsTUFBTUMsTUFBTSxNQUFNQyxLQUNmQyxLQUFLLENBQ0osQ0FDRUMsTUFBa0MsSUFBSSxNQUN2QyxvQ0FBaUNmLE9BQU9NLFFBQVEsSUFDakQ7WUFBRVksUUFBUVgsV0FBV1csTUFBTTtRQUFDLEdBRTdCQyxPQUFPLENBQUM7WUFDUEMsYUFBYVg7UUFDZjtRQUNGLElBQUksQ0FBQ0csSUFBSVMsRUFBRSxJQUFJVCxJQUFJVSxNQUFNLEtBQUssS0FBSztZQUNqQyxPQUFPQyxRQUFRQyxNQUFNLENBQUMsSUFBSUMsTUFBTSxNQUFNYixJQUFJYyxJQUFJO1FBQ2hEO1FBRUEsTUFBTTNCLE9BQW1DLE1BQU1hLElBQUllLElBQUk7WUFPbERqQztRQU5MLE9BQU87WUFDTGtDLE9BQU87WUFDUEMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLFVBQVUsQ0FBQ0MsUUFFVCxDQUFDdEMsT0FBQUEsQ0FBQUEsQ0FBQUEsZUFBQUEsT0FBT3VDLElBQUkscUJBQVh2QyxhQUFhd0MsUUFBUSxDQUFDLHNCQUNyQm5DLDJCQUFBQSxLQUFLb0Msa0JBQWtCLHNCQUF2QnBDLGdDQUFBQSx5QkFBeUJrQyxJQUFJLHFCQUE3QmxDLDhCQUErQm1DLFFBQVEsQ0FBQyxzQkFDeENuQyw0QkFBQUEsS0FBS29DLGtCQUFrQixzQkFBdkJwQyxpQ0FBQUEsMEJBQXlCa0MsSUFBSSxxQkFBN0JsQywrQkFBK0JxQyxVQUFVLENBQUMsNEJBRjNDMUMsT0FHQztZQUVKMkMsa0JBQWtCM0M7WUFDbEJ5QyxvQkFBb0JwQyxLQUFLb0Msa0JBQWtCO1lBQzNDRyxtQkFBbUJ2QyxLQUFLdUMsaUJBQWlCLElBQUk7WUFDN0NDLGVBQWV4QyxLQUFLd0MsYUFBYTtRQUNuQztJQUNGO0lBRUEsSUFDRTdDLE9BQU91QyxJQUFJLEtBQUssbUJBQ2hCdkMsZUFBQUEsT0FBT3VDLElBQUkscUJBQVh2QyxhQUFhOEMsS0FBSyxDQUFDLGdCQUNuQjlDLGdCQUFBQSxPQUFPdUMsSUFBSSxxQkFBWHZDLGNBQWE4QyxLQUFLLENBQUMsaUJBQ25CO1FBQ0EsT0FBT2pCLFFBQVFrQixPQUFPLENBQUM7WUFDckJiLE9BQU87WUFDUEMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLFVBQVU7WUFDVk0sa0JBQWtCM0M7WUFDbEJ5QyxvQkFBb0I7WUFDcEJHLG1CQUFtQjtZQUNuQkMsZUFBZTtRQUNqQjtJQUNGO0lBRUEsT0FBT3pDLHlCQUF5QjRDLEtBQUssQ0FBQyxDQUFDQztZQUU3QkEsY0FBQUE7ZUFGNkM7WUFDckRmLE9BQU87WUFDUEMsUUFBUWMsQ0FBQUEsT0FBQUEsQ0FBQUEsZUFBQUEsT0FBQUEsT0FBQUEsS0FBQUEsSUFBQUEsSUFBS0MsT0FBTyxZQUFaRCxlQUFnQkEsT0FBQUEsT0FBQUEsS0FBQUEsSUFBQUEsSUFBS3JDLFFBQVEsY0FBN0JxQyxPQUFtQztZQUMzQ2IsVUFBVTtZQUNWQyxVQUFVO1lBQ1ZNLGtCQUFrQjNDO1lBQ2xCeUMsb0JBQW9CO1lBQ3BCRyxtQkFBbUI7WUFDbkJDLGVBQWU7UUFDakI7SUFBQTtBQUNGO0FBRU8sU0FBUy9DLHVCQUNkcUQsTUFBb0IsRUFDcEJsRCxJQUFxQyxFQUNyQ0MsUUFBaUIsRUFDakJDLFlBQW9CO0lBRXBCLE9BQU8wQixRQUFRdUIsR0FBRyxDQUNoQkQsT0FBT0UsR0FBRyxDQUFDLENBQUNDLFFBQ1Z2RCxzQkFBc0J1RCxPQUFPckQsTUFBTUMsVUFBVUM7QUFHbkQ7QUFFQSxNQUFNb0QsaUJBQWlCO0lBQ3JCO0lBQ0E7Q0FDRDtBQUVELFNBQVNDLGlCQUFpQmpCLElBQVk7SUFDcEMsT0FBT2dCLGVBQWVFLElBQUksQ0FBQyxDQUFDQyxRQUFVQSxNQUFNQyxJQUFJLENBQUNwQjtBQUNuRDtBQUVBOzs7Ozs7Q0FNQyxHQUNELFNBQVNxQixzQkFBc0JyQixJQUFZO0lBQ3pDLEtBQUssTUFBTXNCLFNBQVNOLGVBQWdCaEIsT0FBT0EsS0FBS3VCLE9BQU8sQ0FBQ0QsT0FBTztJQUMvRCxPQUFPdEI7QUFDVDtBQUVPLFNBQVMxQyxlQUFleUQsS0FBaUI7SUFDOUMsSUFBSSxDQUFDQSxNQUFNZixJQUFJLEVBQUUsT0FBTztJQUV4QixJQUFJd0IsTUFBTTtJQUNWLElBQUk7WUFJRUM7UUFISixNQUFNQyxJQUFJLElBQUlDLElBQUlaLE1BQU1mLElBQUk7UUFFNUIsNENBQTRDO1FBQzVDLElBQUl5QixDQUFBQSxDQUFBQSx1QkFBQUEsV0FBV0csUUFBUSxxQkFBbkJILHFCQUFxQkksTUFBTSxNQUFLSCxFQUFFRyxNQUFNLEVBQUU7WUFDNUMsZ0VBQWdFO1lBQ2hFLDhDQUE4QztZQUM5QyxJQUFJSCxFQUFFRyxNQUFNLEtBQUssUUFBUTtnQkFDdkJMLE9BQU9FLEVBQUVJLFFBQVE7WUFDbkIsT0FBTztnQkFDTE4sT0FBT0UsRUFBRUcsTUFBTTtZQUNqQjtRQUNGO1FBRUEscUVBQXFFO1FBQ3JFLGNBQWM7UUFDZEwsT0FBT0UsRUFBRUssUUFBUTtRQUNqQlAsT0FBTztRQUNQQSxNQUFNSCxzQkFBc0JHO0lBQzlCLEVBQUUsT0FBQVEsR0FBTTtRQUNOUixPQUFPSCxzQkFBc0JOLE1BQU1mLElBQUksSUFBSSxlQUFlO0lBQzVEO0lBRUEsSUFBSSxDQUFDaUIsaUJBQWlCRixNQUFNZixJQUFJLEtBQUtlLE1BQU1rQixVQUFVLElBQUksTUFBTTtRQUM3RCxJQUFJbEIsTUFBTW1CLE1BQU0sSUFBSSxNQUFNO1lBQ3hCVixPQUFPLE1BQUlULE1BQU1rQixVQUFVLEdBQUMsTUFBR2xCLE1BQU1tQixNQUFNLEdBQUM7UUFDOUMsT0FBTztZQUNMVixPQUFPLE1BQUlULE1BQU1rQixVQUFVLEdBQUM7UUFDOUI7SUFDRjtJQUNBLE9BQU9ULElBQUlXLEtBQUssQ0FBQyxHQUFHLENBQUM7QUFDdkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9oZWxwZXJzL3N0YWNrLWZyYW1lLnRzPzY2N2UiXSwibmFtZXMiOlsiZ2V0RnJhbWVTb3VyY2UiLCJnZXRPcmlnaW5hbFN0YWNrRnJhbWVzIiwiZ2V0T3JpZ2luYWxTdGFja0ZyYW1lIiwic291cmNlIiwidHlwZSIsImlzQXBwRGlyIiwiZXJyb3JNZXNzYWdlIiwiX2dldE9yaWdpbmFsU3RhY2tGcmFtZSIsImJvZHkiLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJhcHBlbmQiLCJTdHJpbmciLCJrZXkiLCJfc291cmNlX2tleSIsInRvU3RyaW5nIiwiY29udHJvbGxlciIsIkFib3J0Q29udHJvbGxlciIsInRtIiwic2V0VGltZW91dCIsImFib3J0IiwicmVzIiwic2VsZiIsImZldGNoIiwicHJvY2VzcyIsImVudiIsIl9fTkVYVF9ST1VURVJfQkFTRVBBVEgiLCJzaWduYWwiLCJmaW5hbGx5IiwiY2xlYXJUaW1lb3V0Iiwib2siLCJzdGF0dXMiLCJQcm9taXNlIiwicmVqZWN0IiwiRXJyb3IiLCJ0ZXh0IiwianNvbiIsImVycm9yIiwicmVhc29uIiwiZXh0ZXJuYWwiLCJleHBhbmRlZCIsIkJvb2xlYW4iLCJmaWxlIiwiaW5jbHVkZXMiLCJvcmlnaW5hbFN0YWNrRnJhbWUiLCJzdGFydHNXaXRoIiwic291cmNlU3RhY2tGcmFtZSIsIm9yaWdpbmFsQ29kZUZyYW1lIiwic291cmNlUGFja2FnZSIsIm1hdGNoIiwicmVzb2x2ZSIsImNhdGNoIiwiZXJyIiwibWVzc2FnZSIsImZyYW1lcyIsImFsbCIsIm1hcCIsImZyYW1lIiwid2VicGFja1JlZ0V4ZXMiLCJpc1dlYnBhY2tCdW5kbGVkIiwic29tZSIsInJlZ0V4IiwidGVzdCIsImZvcm1hdEZyYW1lU291cmNlRmlsZSIsInJlZ2V4IiwicmVwbGFjZSIsInN0ciIsImdsb2JhbFRoaXMiLCJ1IiwiVVJMIiwibG9jYXRpb24iLCJvcmlnaW4iLCJwcm90b2NvbCIsInBhdGhuYW1lIiwiZSIsImxpbmVOdW1iZXIiLCJjb2x1bW4iLCJzbGljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-error-handler.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-error-handler.js ***!
  \**********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useErrorHandler\", ({\n    enumerable: true,\n    get: function() {\n        return useErrorHandler;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ./hydration-error-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/hydration-error-info.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../../../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _ishydrationerror = __webpack_require__(/*! ../../../is-hydration-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nif (typeof window !== \"undefined\") {\n    try {\n        // Increase the number of stack frames on the client\n        Error.stackTraceLimit = 50;\n    } catch (e) {}\n}\nlet hasHydrationError = false;\nconst errorQueue = [];\nconst rejectionQueue = [];\nconst errorHandlers = [];\nconst rejectionHandlers = [];\nif (typeof window !== \"undefined\") {\n    // These event handlers must be added outside of the hook because there is no\n    // guarantee that the hook will be alive in a mounted component in time to\n    // when the errors occur.\n    window.addEventListener(\"error\", (ev)=>{\n        if ((0, _isnextroutererror.isNextRouterError)(ev.error)) {\n            ev.preventDefault();\n            return;\n        }\n        const error = ev == null ? void 0 : ev.error;\n        if (!error || !(error instanceof Error) || typeof error.stack !== \"string\") {\n            // A non-error was thrown, we don't have anything to show. :-(\n            return;\n        }\n        const isCausedByHydrationFailure = (0, _ishydrationerror.isHydrationError)(error);\n        if ((0, _ishydrationerror.isHydrationError)(error) && !error.message.includes(\"https://nextjs.org/docs/messages/react-hydration-error\")) {\n            // If there's any extra information in the error message to display,\n            // append it to the error message details property\n            if (_hydrationerrorinfo.hydrationErrorState.warning) {\n                error.details = {\n                    ...error.details,\n                    // It contains the warning, component stack, server and client tag names\n                    ..._hydrationerrorinfo.hydrationErrorState\n                };\n            }\n            error.message += \"\\nSee more info here: https://nextjs.org/docs/messages/react-hydration-error\";\n        }\n        const e = error;\n        // Only queue one hydration every time\n        if (isCausedByHydrationFailure) {\n            if (!hasHydrationError) {\n                errorQueue.push(e);\n            }\n            hasHydrationError = true;\n        }\n        for (const handler of errorHandlers){\n            handler(e);\n        }\n    });\n    window.addEventListener(\"unhandledrejection\", (ev)=>{\n        const reason = ev == null ? void 0 : ev.reason;\n        if (!reason || !(reason instanceof Error) || typeof reason.stack !== \"string\") {\n            // A non-error was thrown, we don't have anything to show. :-(\n            return;\n        }\n        const e = reason;\n        rejectionQueue.push(e);\n        for (const handler of rejectionHandlers){\n            handler(e);\n        }\n    });\n}\nfunction useErrorHandler(handleOnUnhandledError, handleOnUnhandledRejection) {\n    (0, _react.useEffect)(()=>{\n        // Handle queued errors.\n        errorQueue.forEach(handleOnUnhandledError);\n        rejectionQueue.forEach(handleOnUnhandledRejection);\n        // Listen to new errors.\n        errorHandlers.push(handleOnUnhandledError);\n        rejectionHandlers.push(handleOnUnhandledRejection);\n        return ()=>{\n            // Remove listeners.\n            errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1);\n            rejectionHandlers.splice(rejectionHandlers.indexOf(handleOnUnhandledRejection), 1);\n        };\n    }, [\n        handleOnUnhandledError,\n        handleOnUnhandledRejection\n    ]);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-error-handler.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-error-handler.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.js ***!
  \***********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useOpenInEditor\", ({\n    enumerable: true,\n    get: function() {\n        return useOpenInEditor;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction useOpenInEditor(param) {\n    let { file, lineNumber, column } = param === void 0 ? {} : param;\n    const openInEditor = (0, _react.useCallback)(()=>{\n        if (file == null || lineNumber == null || column == null) return;\n        const params = new URLSearchParams();\n        params.append(\"file\", file);\n        params.append(\"lineNumber\", String(lineNumber));\n        params.append(\"column\", String(column));\n        self.fetch(( false || \"\") + \"/__nextjs_launch-editor?\" + params.toString()).then(()=>{}, ()=>{\n            console.error(\"There was an issue opening this code in your editor.\");\n        });\n    }, [\n        file,\n        lineNumber,\n        column\n    ]);\n    return openInEditor;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-open-in-editor.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy91c2Utb3Blbi1pbi1lZGl0b3IuanMiLCJtYXBwaW5ncyI6Ijs7OzttREFFZ0JBOzs7ZUFBQUE7OzttQ0FGWTtBQUVyQixTQUFTQSxnQkFBZ0JDLEtBQUE7SUFBQSxNQUM5QkMsSUFBSSxFQUNKQyxVQUFVLEVBQ1ZDLE1BQU0sRUFLUCxHQVIrQkgsVUFBQSxTQVE1QixDQUFDLElBUjJCQTtJQVM5QixNQUFNSSxlQUFlQyxDQUFBQSxHQUFBQSxPQUFBQSxXQUFXLEVBQUM7UUFDL0IsSUFBSUosUUFBUSxRQUFRQyxjQUFjLFFBQVFDLFVBQVUsTUFBTTtRQUUxRCxNQUFNRyxTQUFTLElBQUlDO1FBQ25CRCxPQUFPRSxNQUFNLENBQUMsUUFBUVA7UUFDdEJLLE9BQU9FLE1BQU0sQ0FBQyxjQUFjQyxPQUFPUDtRQUNuQ0ksT0FBT0UsTUFBTSxDQUFDLFVBQVVDLE9BQU9OO1FBRS9CTyxLQUNHQyxLQUFLLENBQ0osQ0FDRUMsTUFBa0MsSUFBSSxNQUN2Qyw2QkFBMEJOLE9BQU9TLFFBQVEsSUFFM0NDLElBQUksQ0FDSCxLQUFPLEdBQ1A7WUFDRUMsUUFBUUMsS0FBSyxDQUFDO1FBQ2hCO0lBRU4sR0FBRztRQUFDakI7UUFBTUM7UUFBWUM7S0FBTztJQUU3QixPQUFPQztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy91c2Utb3Blbi1pbi1lZGl0b3IudHM/MDg4NiJdLCJuYW1lcyI6WyJ1c2VPcGVuSW5FZGl0b3IiLCJwYXJhbSIsImZpbGUiLCJsaW5lTnVtYmVyIiwiY29sdW1uIiwib3BlbkluRWRpdG9yIiwidXNlQ2FsbGJhY2siLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJhcHBlbmQiLCJTdHJpbmciLCJzZWxmIiwiZmV0Y2giLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsInRvU3RyaW5nIiwidGhlbiIsImNvbnNvbGUiLCJlcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-websocket.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-websocket.js ***!
  \******************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    useSendMessage: function() {\n        return useSendMessage;\n    },\n    useTurbopack: function() {\n        return useTurbopack;\n    },\n    useWebsocket: function() {\n        return useWebsocket;\n    },\n    useWebsocketPing: function() {\n        return useWebsocketPing;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _getsocketurl = __webpack_require__(/*! ./get-socket-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/get-socket-url.js\");\nfunction useWebsocket(assetPrefix) {\n    const webSocketRef = (0, _react.useRef)();\n    (0, _react.useEffect)(()=>{\n        if (webSocketRef.current) {\n            return;\n        }\n        const url = (0, _getsocketurl.getSocketUrl)(assetPrefix);\n        webSocketRef.current = new window.WebSocket(\"\" + url + \"/_next/webpack-hmr\");\n    }, [\n        assetPrefix\n    ]);\n    return webSocketRef;\n}\nfunction useSendMessage(webSocketRef) {\n    const sendMessage = (0, _react.useCallback)((data)=>{\n        const socket = webSocketRef.current;\n        if (!socket || socket.readyState !== socket.OPEN) {\n            return;\n        }\n        return socket.send(data);\n    }, [\n        webSocketRef\n    ]);\n    return sendMessage;\n}\nfunction useTurbopack(sendMessage, onUpdateError) {\n    const turbopackState = (0, _react.useRef)({\n        init: false,\n        // Until the dynamic import resolves, queue any turbopack messages which will be replayed.\n        queue: [],\n        callback: undefined\n    });\n    const processTurbopackMessage = (0, _react.useCallback)((msg)=>{\n        const { callback, queue } = turbopackState.current;\n        if (callback) {\n            callback(msg);\n        } else {\n            queue.push(msg);\n        }\n    }, []);\n    (0, _react.useEffect)(()=>{\n        const { current: initCurrent } = turbopackState;\n        // TODO(WEB-1589): only install if `process.turbopack` set.\n        if (initCurrent.init) {\n            return;\n        }\n        initCurrent.init = true;\n        Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! @vercel/turbopack-ecmascript-runtime/dev/client/hmr-client.ts */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/noop-turbopack-hmr.js\"))).then((param)=>{\n            let { connect } = param;\n            const { current } = turbopackState;\n            connect({\n                addMessageListener (cb) {\n                    current.callback = cb;\n                    // Replay all Turbopack messages before we were able to establish the HMR client.\n                    for (const msg of current.queue){\n                        cb(msg);\n                    }\n                    current.queue = undefined;\n                },\n                sendMessage,\n                onUpdateError\n            });\n        });\n    }, [\n        sendMessage,\n        onUpdateError\n    ]);\n    return processTurbopackMessage;\n}\nfunction useWebsocketPing(websocketRef) {\n    _s();\n    const sendMessage = useSendMessage(websocketRef);\n    const { tree } = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    (0, _react.useEffect)(()=>{\n        // Taken from on-demand-entries-client.js\n        const interval = setInterval(()=>{\n            sendMessage(JSON.stringify({\n                event: \"ping\",\n                tree,\n                appDirRoute: true\n            }));\n        }, 2500);\n        return ()=>clearInterval(interval);\n    }, [\n        tree,\n        sendMessage\n    ]);\n}\n_s(useWebsocketPing, \"wUse5NG7XMV1uhKK1kY0LLDje8k=\", false, function() {\n    return [\n        useSendMessage\n    ];\n});\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-websocket.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-websocket.js\n"));

/***/ })

}]);