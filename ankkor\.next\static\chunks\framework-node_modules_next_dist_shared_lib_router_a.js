"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_shared_lib_router_a"],{

/***/ "./node_modules/next/dist/shared/lib/router/adapters.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/adapters.js ***!
  \**************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PathnameContextProviderAdapter: function() {\n        return PathnameContextProviderAdapter;\n    },\n    adaptForAppRouterInstance: function() {\n        return adaptForAppRouterInstance;\n    },\n    adaptForPathParams: function() {\n        return adaptForPathParams;\n    },\n    adaptForSearchParams: function() {\n        return adaptForSearchParams;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../hooks-client-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"./node_modules/next/dist/shared/lib/router/utils/index.js\");\nconst _aspathtosearchparams = __webpack_require__(/*! ./utils/as-path-to-search-params */ \"./node_modules/next/dist/shared/lib/router/utils/as-path-to-search-params.js\");\nconst _routeregex = __webpack_require__(/*! ./utils/route-regex */ \"./node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nfunction adaptForAppRouterInstance(pagesRouter) {\n    return {\n        back () {\n            pagesRouter.back();\n        },\n        forward () {\n            pagesRouter.forward();\n        },\n        refresh () {\n            pagesRouter.reload();\n        },\n        fastRefresh () {},\n        push (href, param) {\n            let { scroll } = param === void 0 ? {} : param;\n            void pagesRouter.push(href, undefined, {\n                scroll\n            });\n        },\n        replace (href, param) {\n            let { scroll } = param === void 0 ? {} : param;\n            void pagesRouter.replace(href, undefined, {\n                scroll\n            });\n        },\n        prefetch (href) {\n            void pagesRouter.prefetch(href);\n        }\n    };\n}\nfunction adaptForSearchParams(router) {\n    if (!router.isReady || !router.query) {\n        return new URLSearchParams();\n    }\n    return (0, _aspathtosearchparams.asPathToSearchParams)(router.asPath);\n}\nfunction adaptForPathParams(router) {\n    if (!router.isReady || !router.query) {\n        return null;\n    }\n    const pathParams = {};\n    const routeRegex = (0, _routeregex.getRouteRegex)(router.pathname);\n    const keys = Object.keys(routeRegex.groups);\n    for (const key of keys){\n        pathParams[key] = router.query[key];\n    }\n    return pathParams;\n}\nfunction PathnameContextProviderAdapter(param) {\n    let { children, router, ...props } = param;\n    const ref = (0, _react.useRef)(props.isAutoExport);\n    const value = (0, _react.useMemo)(()=>{\n        // isAutoExport is only ever `true` on the first render from the server,\n        // so reset it to `false` after we read it for the first time as `true`. If\n        // we don't use the value, then we don't need it.\n        const isAutoExport = ref.current;\n        if (isAutoExport) {\n            ref.current = false;\n        }\n        // When the route is a dynamic route, we need to do more processing to\n        // determine if we need to stop showing the pathname.\n        if ((0, _utils.isDynamicRoute)(router.pathname)) {\n            // When the router is rendering the fallback page, it can't possibly know\n            // the path, so return `null` here. Read more about fallback pages over\n            // at:\n            // https://nextjs.org/docs/api-reference/data-fetching/get-static-paths#fallback-pages\n            if (router.isFallback) {\n                return null;\n            }\n            // When `isAutoExport` is true, meaning this is a page page has been\n            // automatically statically optimized, and the router is not ready, then\n            // we can't know the pathname yet. Read more about automatic static\n            // optimization at:\n            // https://nextjs.org/docs/advanced-features/automatic-static-optimization\n            if (isAutoExport && !router.isReady) {\n                return null;\n            }\n        }\n        // The `router.asPath` contains the pathname seen by the browser (including\n        // any query strings), so it should have that stripped. Read more about the\n        // `asPath` option over at:\n        // https://nextjs.org/docs/api-reference/next/router#router-object\n        let url;\n        try {\n            url = new URL(router.asPath, \"http://f\");\n        } catch (_) {\n            // fallback to / for invalid asPath values e.g. //\n            return \"/\";\n        }\n        return url.pathname;\n    }, [\n        router.asPath,\n        router.isFallback,\n        router.isReady,\n        router.pathname\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n        value: value,\n        children: children\n    });\n} //# sourceMappingURL=adapters.js.map\n_c = PathnameContextProviderAdapter;\nvar _c;\n$RefreshReg$(_c, \"PathnameContextProviderAdapter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/adapters.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/router/utils/add-locale.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/add-locale.js ***!
  \**********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"addLocale\", ({\n    enumerable: true,\n    get: function() {\n        return addLocale;\n    }\n}));\nconst _addpathprefix = __webpack_require__(/*! ./add-path-prefix */ \"./node_modules/next/dist/shared/lib/router/utils/add-path-prefix.js\");\nconst _pathhasprefix = __webpack_require__(/*! ./path-has-prefix */ \"./node_modules/next/dist/shared/lib/router/utils/path-has-prefix.js\");\nfunction addLocale(path, locale, defaultLocale, ignorePrefix) {\n    // If no locale was given or the locale is the default locale, we don't need\n    // to prefix the path.\n    if (!locale || locale === defaultLocale) return path;\n    const lower = path.toLowerCase();\n    // If the path is an API path or the path already has the locale prefix, we\n    // don't need to prefix the path.\n    if (!ignorePrefix) {\n        if ((0, _pathhasprefix.pathHasPrefix)(lower, \"/api\")) return path;\n        if ((0, _pathhasprefix.pathHasPrefix)(lower, \"/\" + locale.toLowerCase())) return path;\n    }\n    // Add the locale prefix to the path.\n    return (0, _addpathprefix.addPathPrefix)(path, \"/\" + locale);\n} //# sourceMappingURL=add-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2FkZC1sb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs2Q0FRZ0JBOzs7ZUFBQUE7OzsyQ0FSYzsyQ0FDQTtBQU92QixTQUFTQSxVQUNkQyxJQUFZLEVBQ1pDLE1BQXVCLEVBQ3ZCQyxhQUFzQixFQUN0QkMsWUFBc0I7SUFFdEIsNEVBQTRFO0lBQzVFLHNCQUFzQjtJQUN0QixJQUFJLENBQUNGLFVBQVVBLFdBQVdDLGVBQWUsT0FBT0Y7SUFFaEQsTUFBTUksUUFBUUosS0FBS0ssV0FBVztJQUU5QiwyRUFBMkU7SUFDM0UsaUNBQWlDO0lBQ2pDLElBQUksQ0FBQ0YsY0FBYztRQUNqQixJQUFJRyxDQUFBQSxHQUFBQSxlQUFBQSxhQUFhLEVBQUNGLE9BQU8sU0FBUyxPQUFPSjtRQUN6QyxJQUFJTSxDQUFBQSxHQUFBQSxlQUFBQSxhQUFhLEVBQUNGLE9BQU8sTUFBSUgsT0FBT0ksV0FBVyxLQUFPLE9BQU9MO0lBQy9EO0lBRUEscUNBQXFDO0lBQ3JDLE9BQU9PLENBQUFBLEdBQUFBLGVBQUFBLGFBQWEsRUFBQ1AsTUFBTSxNQUFJQztBQUNqQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2FkZC1sb2NhbGUudHM/MDI0OSJdLCJuYW1lcyI6WyJhZGRMb2NhbGUiLCJwYXRoIiwibG9jYWxlIiwiZGVmYXVsdExvY2FsZSIsImlnbm9yZVByZWZpeCIsImxvd2VyIiwidG9Mb3dlckNhc2UiLCJwYXRoSGFzUHJlZml4IiwiYWRkUGF0aFByZWZpeCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/utils/add-locale.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/action-queue.js ***!
  \******************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ActionQueueContext: function() {\n        return ActionQueueContext;\n    },\n    createMutableActionQueue: function() {\n        return createMutableActionQueue;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../../../client/components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _routerreducer = __webpack_require__(/*! ../../../client/components/router-reducer/router-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst ActionQueueContext = _react.default.createContext(null);\nfunction runRemainingActions(actionQueue, setState) {\n    if (actionQueue.pending !== null) {\n        actionQueue.pending = actionQueue.pending.next;\n        if (actionQueue.pending !== null) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            runAction({\n                actionQueue,\n                action: actionQueue.pending,\n                setState\n            });\n        } else {\n            // No more actions are pending, check if a refresh is needed\n            if (actionQueue.needsRefresh) {\n                actionQueue.needsRefresh = false;\n                actionQueue.dispatch({\n                    type: _routerreducertypes.ACTION_REFRESH,\n                    origin: window.location.origin\n                }, setState);\n            }\n        }\n    }\n}\nasync function runAction(param) {\n    let { actionQueue, action, setState } = param;\n    const prevState = actionQueue.state;\n    if (!prevState) {\n        // This shouldn't happen as the state is initialized in the dispatcher if it's not set\n        throw new Error(\"Invariant: Router state not initialized\");\n    }\n    actionQueue.pending = action;\n    const payload = action.payload;\n    const actionResult = actionQueue.action(prevState, payload);\n    function handleResult(nextState) {\n        // if we discarded this action, the state should also be discarded\n        if (action.discarded) {\n            return;\n        }\n        actionQueue.state = nextState;\n        if (actionQueue.devToolsInstance) {\n            actionQueue.devToolsInstance.send(payload, nextState);\n        }\n        runRemainingActions(actionQueue, setState);\n        action.resolve(nextState);\n    }\n    // if the action is a promise, set up a callback to resolve it\n    if ((0, _routerreducertypes.isThenable)(actionResult)) {\n        actionResult.then(handleResult, (err)=>{\n            runRemainingActions(actionQueue, setState);\n            action.reject(err);\n        });\n    } else {\n        handleResult(actionResult);\n    }\n}\nfunction dispatchAction(actionQueue, payload, setState) {\n    let resolvers = {\n        resolve: setState,\n        reject: ()=>{}\n    };\n    // most of the action types are async with the exception of restore\n    // it's important that restore is handled quickly since it's fired on the popstate event\n    // and we don't want to add any delay on a back/forward nav\n    // this only creates a promise for the async actions\n    if (payload.type !== _routerreducertypes.ACTION_RESTORE) {\n        // Create the promise and assign the resolvers to the object.\n        const deferredPromise = new Promise((resolve, reject)=>{\n            resolvers = {\n                resolve,\n                reject\n            };\n        });\n        (0, _react.startTransition)(()=>{\n            // we immediately notify React of the pending promise -- the resolver is attached to the action node\n            // and will be called when the associated action promise resolves\n            setState(deferredPromise);\n        });\n    }\n    const newAction = {\n        payload,\n        next: null,\n        resolve: resolvers.resolve,\n        reject: resolvers.reject\n    };\n    // Check if the queue is empty\n    if (actionQueue.pending === null) {\n        // The queue is empty, so add the action and start it immediately\n        // Mark this action as the last in the queue\n        actionQueue.last = newAction;\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else if (payload.type === _routerreducertypes.ACTION_NAVIGATE || payload.type === _routerreducertypes.ACTION_RESTORE) {\n        // Navigations (including back/forward) take priority over any pending actions.\n        // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n        actionQueue.pending.discarded = true;\n        // Mark this action as the last in the queue\n        actionQueue.last = newAction;\n        // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n        if (actionQueue.pending.payload.type === _routerreducertypes.ACTION_SERVER_ACTION) {\n            actionQueue.needsRefresh = true;\n        }\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else {\n        // The queue is not empty, so add the action to the end of the queue\n        // It will be started by runRemainingActions after the previous action finishes\n        if (actionQueue.last !== null) {\n            actionQueue.last.next = newAction;\n        }\n        actionQueue.last = newAction;\n    }\n}\nfunction createMutableActionQueue() {\n    const actionQueue = {\n        state: null,\n        dispatch: (payload, setState)=>dispatchAction(actionQueue, payload, setState),\n        action: async (state, action)=>{\n            if (state === null) {\n                throw new Error(\"Invariant: Router state not initialized\");\n            }\n            const result = (0, _routerreducer.reducer)(state, action);\n            return result;\n        },\n        pending: null,\n        last: null\n    };\n    return actionQueue;\n} //# sourceMappingURL=action-queue.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js\n"));

/***/ })

}]);