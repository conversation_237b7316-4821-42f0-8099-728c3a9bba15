"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("framework-node_modules_next_dist_client_components_rea",{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/shared.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/shared.js ***!
  \******************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_BEFORE_REFRESH: function() {\n        return ACTION_BEFORE_REFRESH;\n    },\n    ACTION_BUILD_ERROR: function() {\n        return ACTION_BUILD_ERROR;\n    },\n    ACTION_BUILD_OK: function() {\n        return ACTION_BUILD_OK;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_UNHANDLED_ERROR: function() {\n        return ACTION_UNHANDLED_ERROR;\n    },\n    ACTION_UNHANDLED_REJECTION: function() {\n        return ACTION_UNHANDLED_REJECTION;\n    },\n    ACTION_VERSION_INFO: function() {\n        return ACTION_VERSION_INFO;\n    },\n    INITIAL_OVERLAY_STATE: function() {\n        return INITIAL_OVERLAY_STATE;\n    },\n    REACT_REFRESH_FULL_RELOAD_FROM_ERROR: function() {\n        return REACT_REFRESH_FULL_RELOAD_FROM_ERROR;\n    },\n    useErrorOverlayReducer: function() {\n        return useErrorOverlayReducer;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nconst ACTION_BUILD_OK = \"build-ok\";\nconst ACTION_BUILD_ERROR = \"build-error\";\nconst ACTION_BEFORE_REFRESH = \"before-fast-refresh\";\nconst ACTION_REFRESH = \"fast-refresh\";\nconst ACTION_VERSION_INFO = \"version-info\";\nconst ACTION_UNHANDLED_ERROR = \"unhandled-error\";\nconst ACTION_UNHANDLED_REJECTION = \"unhandled-rejection\";\nfunction pushErrorFilterDuplicates(errors, err) {\n    return [\n        ...errors.filter((e)=>{\n            // Filter out duplicate errors\n            return e.event.reason !== err.event.reason;\n        }),\n        err\n    ];\n}\nconst INITIAL_OVERLAY_STATE = {\n    nextId: 1,\n    buildError: null,\n    errors: [],\n    notFound: false,\n    refreshState: {\n        type: \"idle\"\n    },\n    rootLayoutMissingTags: [],\n    versionInfo: {\n        installed: \"0.0.0\",\n        staleness: \"unknown\"\n    }\n};\nfunction useErrorOverlayReducer() {\n    return (0, _react.useReducer)((_state, action)=>{\n        switch(action.type){\n            case ACTION_BUILD_OK:\n                {\n                    return {\n                        ..._state,\n                        buildError: null\n                    };\n                }\n            case ACTION_BUILD_ERROR:\n                {\n                    return {\n                        ..._state,\n                        buildError: action.message\n                    };\n                }\n            case ACTION_BEFORE_REFRESH:\n                {\n                    return {\n                        ..._state,\n                        refreshState: {\n                            type: \"pending\",\n                            errors: []\n                        }\n                    };\n                }\n            case ACTION_REFRESH:\n                {\n                    return {\n                        ..._state,\n                        buildError: null,\n                        errors: // and UNHANDLED_REJECTION events might be dispatched between the\n                        // BEFORE_REFRESH and the REFRESH event. We want to keep those errors\n                        // around until the next refresh. Otherwise we run into a race\n                        // condition where those errors would be cleared on refresh completion\n                        // before they can be displayed.\n                        _state.refreshState.type === \"pending\" ? _state.refreshState.errors : [],\n                        refreshState: {\n                            type: \"idle\"\n                        }\n                    };\n                }\n            case ACTION_UNHANDLED_ERROR:\n            case ACTION_UNHANDLED_REJECTION:\n                {\n                    switch(_state.refreshState.type){\n                        case \"idle\":\n                            {\n                                return {\n                                    ..._state,\n                                    nextId: _state.nextId + 1,\n                                    errors: pushErrorFilterDuplicates(_state.errors, {\n                                        id: _state.nextId,\n                                        event: action\n                                    })\n                                };\n                            }\n                        case \"pending\":\n                            {\n                                return {\n                                    ..._state,\n                                    nextId: _state.nextId + 1,\n                                    refreshState: {\n                                        ..._state.refreshState,\n                                        errors: pushErrorFilterDuplicates(_state.refreshState.errors, {\n                                            id: _state.nextId,\n                                            event: action\n                                        })\n                                    }\n                                };\n                            }\n                        default:\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const _ = _state.refreshState;\n                            return _state;\n                    }\n                }\n            case ACTION_VERSION_INFO:\n                {\n                    return {\n                        ..._state,\n                        versionInfo: action.versionInfo\n                    };\n                }\n            default:\n                {\n                    return _state;\n                }\n        }\n    }, INITIAL_OVERLAY_STATE);\n}\nconst REACT_REFRESH_FULL_RELOAD_FROM_ERROR = \"[Fast Refresh] performing full reload because your application had an unrecoverable error\";\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=shared.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/shared.js\n"));

/***/ })

});