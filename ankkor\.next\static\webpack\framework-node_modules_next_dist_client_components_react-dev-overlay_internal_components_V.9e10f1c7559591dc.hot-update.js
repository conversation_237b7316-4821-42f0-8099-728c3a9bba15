"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_V",{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/VersionStalenessInfo.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/VersionStalenessInfo.js ***!
  \*************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    VersionStalenessInfo: function() {\n        return VersionStalenessInfo;\n    },\n    getStaleness: function() {\n        return getStaleness;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nfunction VersionStalenessInfo(props) {\n    if (!props) return null;\n    const { staleness } = props;\n    let { text, indicatorClass, title } = getStaleness(props);\n    if (!text) return null;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"small\", {\n        className: \"nextjs-container-build-error-version-status\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: indicatorClass\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"small\", {\n                \"data-nextjs-version-checker\": true,\n                title: title,\n                children: text\n            }),\n            \" \",\n            staleness === \"fresh\" || staleness === \"newer-than-npm\" || staleness === \"unknown\" ? null : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                href: \"https://nextjs.org/docs/messages/version-staleness\",\n                children: \"(learn more)\"\n            }),\n             false ? 0 : \"\"\n        ]\n    });\n}\n_c = VersionStalenessInfo;\nfunction getStaleness(param) {\n    let { installed, staleness, expected } = param;\n    let text = \"\";\n    let title = \"\";\n    let indicatorClass = \"\";\n    const versionLabel = \"Next.js (\" + installed + \")\";\n    switch(staleness){\n        case \"newer-than-npm\":\n        case \"fresh\":\n            text = versionLabel;\n            title = \"Latest available version is detected (\" + installed + \").\";\n            indicatorClass = \"fresh\";\n            break;\n        case \"stale-patch\":\n        case \"stale-minor\":\n            text = \"\" + versionLabel + \" out of date\";\n            title = \"There is a newer version (\" + expected + \") available, upgrade recommended! \";\n            indicatorClass = \"stale\";\n            break;\n        case \"stale-major\":\n            {\n                text = \"\" + versionLabel + \" is outdated\";\n                title = \"An outdated version detected (latest is \" + expected + \"), upgrade is highly recommended!\";\n                indicatorClass = \"outdated\";\n                break;\n            }\n        case \"stale-prerelease\":\n            {\n                text = \"\" + versionLabel + \" is outdated\";\n                title = \"There is a newer canary version (\" + expected + \") available, please upgrade! \";\n                indicatorClass = \"stale\";\n                break;\n            }\n        case \"unknown\":\n            break;\n        default:\n            break;\n    }\n    return {\n        text,\n        indicatorClass,\n        title\n    };\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=VersionStalenessInfo.js.map\nvar _c;\n$RefreshReg$(_c, \"VersionStalenessInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/VersionStalenessInfo.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/index.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/index.js ***!
  \**********************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    VersionStalenessInfo: function() {\n        return _VersionStalenessInfo.VersionStalenessInfo;\n    },\n    styles: function() {\n        return _styles.styles;\n    }\n});\nconst _styles = __webpack_require__(/*! ./styles */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/styles.js\");\nconst _VersionStalenessInfo = __webpack_require__(/*! ./VersionStalenessInfo */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/VersionStalenessInfo.js\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvVmVyc2lvblN0YWxlbmVzc0luZm8vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBQ1NBLHNCQUFvQjtlQUFwQkEsc0JBQUFBLG9CQUFvQjs7SUFEcEJDLFFBQU07ZUFBTkEsUUFBQUEsTUFBTTs7O29DQUFRO2tEQUNjIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9WZXJzaW9uU3RhbGVuZXNzSW5mby9pbmRleC50c3g/NjEzZSJdLCJuYW1lcyI6WyJWZXJzaW9uU3RhbGVuZXNzSW5mbyIsInN0eWxlcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/index.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/styles.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/styles.js ***!
  \***********************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _nooptemplate = __webpack_require__(/*! ../../helpers/noop-template */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  .nextjs-container-build-error-version-status {\\n    flex: 1;\\n    text-align: right;\\n  }\\n  .nextjs-container-build-error-version-status small {\\n    margin-left: var(--size-gap);\\n    font-size: var(--size-font-small);\\n  }\\n  .nextjs-container-build-error-version-status a {\\n    font-size: var(--size-font-small);\\n  }\\n  .nextjs-container-build-error-version-status span {\\n    display: inline-block;\\n    width: 10px;\\n    height: 10px;\\n    border-radius: 5px;\\n    background: var(--color-ansi-bright-black);\\n  }\\n  .nextjs-container-build-error-version-status span.fresh {\\n    background: var(--color-ansi-green);\\n  }\\n  .nextjs-container-build-error-version-status span.stale {\\n    background: var(--color-ansi-yellow);\\n  }\\n  .nextjs-container-build-error-version-status span.outdated {\\n    background: var(--color-ansi-red);\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvVmVyc2lvblN0YWxlbmVzc0luZm8vc3R5bGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7MENBZ0NTQTs7O2VBQUFBOzs7OzBDQWhDbUI7Ozs7Ozs7Ozs7QUFFNUIsTUFBTUEsU0FBQUEsQ0FBQUEsR0FBU0MsY0FBQUEsSUFBRyxFQUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvVmVyc2lvblN0YWxlbmVzc0luZm8vc3R5bGVzLnRzPzE3ZTMiXSwibmFtZXMiOlsic3R5bGVzIiwiY3NzIiwiX3RlbXBsYXRlT2JqZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/styles.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/hot-linked-text/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/hot-linked-text/index.js ***!
  \*****************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HotlinkedText\", ({\n    enumerable: true,\n    get: function() {\n        return HotlinkedText;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _magicidentifier = __webpack_require__(/*! ../../../../../../shared/lib/magic-identifier */ \"./node_modules/next/dist/shared/lib/magic-identifier.js\");\nconst linkRegex = /https?:\\/\\/[^\\s/$.?#].[^\\s)'\"]*/i;\nconst splitRegexp = new RegExp(\"(\" + _magicidentifier.MAGIC_IDENTIFIER_REGEX.source + \"|\\\\s+)\");\nconst HotlinkedText = function HotlinkedText(props) {\n    const { text, matcher } = props;\n    const wordsAndWhitespaces = text.split(splitRegexp);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: wordsAndWhitespaces.map((word, index)=>{\n            if (linkRegex.test(word)) {\n                const link = linkRegex.exec(word);\n                const href = link[0];\n                // If link matcher is present but the link doesn't match, don't turn it into a link\n                if (typeof matcher === \"function\" && !matcher(href)) {\n                    return word;\n                }\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.default.Fragment, {\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noreferrer noopener\",\n                        children: word\n                    })\n                }, \"link-\" + index);\n            }\n            try {\n                const decodedWord = (0, _magicidentifier.decodeMagicIdentifier)(word);\n                if (decodedWord !== word) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"i\", {\n                        children: [\n                            \"{\",\n                            decodedWord,\n                            \"}\"\n                        ]\n                    }, \"ident-\" + index);\n                }\n            } catch (e) {\n                return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"i\", {\n                    children: [\n                        \"{\",\n                        word,\n                        \" (decoding failed: \",\n                        \"\" + e,\n                        \")\",\n                        \"}\"\n                    ]\n                }, \"ident-\" + index);\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.default.Fragment, {\n                children: word\n            }, \"text-\" + index);\n        })\n    });\n};\n_c = HotlinkedText;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c;\n$RefreshReg$(_c, \"HotlinkedText\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/hot-linked-text/index.js\n"));

/***/ })

});