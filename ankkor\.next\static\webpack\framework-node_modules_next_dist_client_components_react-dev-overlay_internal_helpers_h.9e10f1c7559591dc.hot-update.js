"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_h",{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/hydration-error-info.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/hydration-error-info.js ***!
  \*************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getHydrationWarningType: function() {\n        return getHydrationWarningType;\n    },\n    hydrationErrorState: function() {\n        return hydrationErrorState;\n    },\n    patchConsoleError: function() {\n        return patchConsoleError;\n    }\n});\nconst getHydrationWarningType = (msg)=>{\n    if (isHtmlTagsWarning(msg)) return \"tag\";\n    if (isTextInTagsMismatchWarning(msg)) return \"text-in-tag\";\n    return \"text\";\n};\nconst isHtmlTagsWarning = (msg)=>Boolean(msg && htmlTagsWarnings.has(msg));\nconst isTextMismatchWarning = (msg)=>textMismatchWarning === msg;\nconst isTextInTagsMismatchWarning = (msg)=>Boolean(msg && textAndTagsMismatchWarnings.has(msg));\nconst isKnownHydrationWarning = (msg)=>isHtmlTagsWarning(msg) || isTextInTagsMismatchWarning(msg) || isTextMismatchWarning(msg);\nconst hydrationErrorState = {};\n// https://github.com/facebook/react/blob/main/packages/react-dom/src/__tests__/ReactDOMHydrationDiff-test.js used as a reference\nconst htmlTagsWarnings = new Set([\n    'Warning: Cannot render a sync or defer <script> outside the main document without knowing its order. Try adding async=\"\" or moving it into the root <head> tag.%s',\n    \"Warning: In HTML, %s cannot be a child of <%s>.%s\\nThis will cause a hydration error.%s\",\n    \"Warning: In HTML, %s cannot be a descendant of <%s>.\\nThis will cause a hydration error.%s\",\n    \"Warning: In HTML, text nodes cannot be a child of <%s>.\\nThis will cause a hydration error.\",\n    \"Warning: In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.\\nThis will cause a hydration error.\",\n    \"Warning: Expected server HTML to contain a matching <%s> in <%s>.%s\",\n    \"Warning: Did not expect server HTML to contain a <%s> in <%s>.%s\"\n]);\nconst textAndTagsMismatchWarnings = new Set([\n    'Warning: Expected server HTML to contain a matching text node for \"%s\" in <%s>.%s',\n    'Warning: Did not expect server HTML to contain the text node \"%s\" in <%s>.%s'\n]);\nconst textMismatchWarning = 'Warning: Text content did not match. Server: \"%s\" Client: \"%s\"%s';\nfunction patchConsoleError() {\n    const prev = console.error;\n    console.error = function(msg, serverContent, clientContent, componentStack) {\n        if (isKnownHydrationWarning(msg)) {\n            hydrationErrorState.warning = [\n                // remove the last %s from the message\n                msg,\n                serverContent,\n                clientContent\n            ];\n            hydrationErrorState.componentStack = componentStack;\n            hydrationErrorState.serverContent = serverContent;\n            hydrationErrorState.clientContent = clientContent;\n        }\n        // @ts-expect-error argument is defined\n        prev.apply(console, arguments);\n    };\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hydration-error-info.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/hydration-error-info.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/nodeStackFrames.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/nodeStackFrames.js ***!
  \********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getFilesystemFrame: function() {\n        return getFilesystemFrame;\n    },\n    getServerError: function() {\n        return getServerError;\n    }\n});\nconst _stacktraceparser = __webpack_require__(/*! next/dist/compiled/stacktrace-parser */ \"./node_modules/next/dist/compiled/stacktrace-parser/stack-trace-parser.cjs.js\");\nconst _errorsource = __webpack_require__(/*! ../../../../../shared/lib/error-source */ \"./node_modules/next/dist/shared/lib/error-source.js\");\nfunction getFilesystemFrame(frame) {\n    const f = {\n        ...frame\n    };\n    if (typeof f.file === \"string\") {\n        if (f.file.startsWith(\"/\") || // Win32:\n        /^[a-z]:\\\\/i.test(f.file) || // Win32 UNC:\n        f.file.startsWith(\"\\\\\\\\\")) {\n            f.file = \"file://\" + f.file;\n        }\n    }\n    return f;\n}\nfunction getServerError(error, type) {\n    let n;\n    try {\n        throw new Error(error.message);\n    } catch (e) {\n        n = e;\n    }\n    n.name = error.name;\n    try {\n        n.stack = n.toString() + \"\\n\" + (0, _stacktraceparser.parse)(error.stack).map(getFilesystemFrame).map((f)=>{\n            let str = \"    at \" + f.methodName;\n            if (f.file) {\n                let loc = f.file;\n                if (f.lineNumber) {\n                    loc += \":\" + f.lineNumber;\n                    if (f.column) {\n                        loc += \":\" + f.column;\n                    }\n                }\n                str += \" (\" + loc + \")\";\n            }\n            return str;\n        }).join(\"\\n\");\n    } catch (e) {\n        n.stack = error.stack;\n    }\n    (0, _errorsource.decorateServerError)(n, type);\n    return n;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=nodeStackFrames.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/nodeStackFrames.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js ***!
  \******************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"noop\", ({\n    enumerable: true,\n    get: function() {\n        return noop;\n    }\n}));\nfunction noop(strings) {\n    for(var _len = arguments.length, keys = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        keys[_key - 1] = arguments[_key];\n    }\n    const lastIndex = strings.length - 1;\n    return strings.slice(0, lastIndex).reduce((p, s, i)=>p + s + keys[i], \"\") + strings[lastIndex];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=noop-template.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvbm9vcC10ZW1wbGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O3dDQUFnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0EsS0FDZEMsT0FBNkI7SUFDN0IsUUFBQUMsT0FBQUMsVUFBQUMsTUFBQSxFQUFBQyxPQUFBLElBQUFDLE1BQUFKLE9BQUEsSUFBQUEsT0FBQSxRQUFBSyxPQUFBLEdBQUFBLE9BQUFMLE1BQUFLLE9BQUE7UUFBR0YsSUFBQUEsQ0FBSEUsT0FBQSxLQUFBSixTQUFBLENBQUFJLEtBQTBCOztJQUUxQixNQUFNQyxZQUFZUCxRQUFRRyxNQUFNLEdBQUc7SUFDbkMsT0FDRUgsUUFBUVEsS0FBSyxDQUFDLEdBQUdELFdBQVdFLE1BQU0sQ0FBQyxDQUFDQyxHQUFHQyxHQUFHQyxJQUFNRixJQUFJQyxJQUFJUCxJQUFJLENBQUNRLEVBQUUsRUFBRSxNQUNqRVosT0FBTyxDQUFDTyxVQUFVO0FBRXRCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9ub29wLXRlbXBsYXRlLnRzP2M2ZTgiXSwibmFtZXMiOlsibm9vcCIsInN0cmluZ3MiLCJfbGVuIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwia2V5cyIsIkFycmF5IiwiX2tleSIsImxhc3RJbmRleCIsInNsaWNlIiwicmVkdWNlIiwicCIsInMiLCJpIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\n"));

/***/ })

});