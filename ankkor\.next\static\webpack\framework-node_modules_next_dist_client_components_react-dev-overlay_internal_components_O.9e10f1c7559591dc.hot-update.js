"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_O",{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/Overlay.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/Overlay.js ***!
  \***********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Overlay\", ({\n    enumerable: true,\n    get: function() {\n        return Overlay;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _maintaintabfocus = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./maintain--tab-focus */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/maintain--tab-focus.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _bodylocker = __webpack_require__(/*! ./body-locker */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/body-locker.js\");\nconst Overlay = function Overlay(param) {\n    _s();\n    let { className, children, fixed } = param;\n    _react.useEffect(()=>{\n        (0, _bodylocker.lock)();\n        return ()=>{\n            (0, _bodylocker.unlock)();\n        };\n    }, []);\n    const [overlay, setOverlay] = _react.useState(null);\n    const onOverlay = _react.useCallback((el)=>{\n        setOverlay(el);\n    }, []);\n    _react.useEffect(()=>{\n        if (overlay == null) {\n            return;\n        }\n        const handle2 = (0, _maintaintabfocus.default)({\n            context: overlay\n        });\n        return ()=>{\n            handle2.disengage();\n        };\n    }, [\n        overlay\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-dialog-overlay\": true,\n        className: className,\n        ref: onOverlay,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-nextjs-dialog-backdrop\": true,\n                \"data-nextjs-dialog-backdrop-fixed\": fixed ? true : undefined\n            }),\n            children\n        ]\n    });\n};\n_s(Overlay, \"7AKWSbA/gHapd2YTyFggUak94M8=\");\n_c = Overlay;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=Overlay.js.map\nvar _c;\n$RefreshReg$(_c, \"Overlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvT3ZlcmxheS9PdmVybGF5LmpzIiwibWFwcGluZ3MiOiI7Ozs7OzsyQ0FpRFNBOzs7ZUFBQUE7Ozs7Ozt1RkFqRFk7NkVBQ0U7d0NBQ007QUFRN0IsTUFBTUEsVUFBa0MsU0FBU0EsUUFBUUMsS0FJeEQ7O0lBSndELE1BQ3ZEQyxTQUFTLEVBQ1RDLFFBQVEsRUFDUkMsS0FBSyxFQUNOLEdBSndESDtJQUt2REksT0FBTUMsU0FBUyxDQUFDO1FBQ2RDLENBQUFBLEdBQUFBLFlBQUFBLElBQUk7UUFDSixPQUFPO1lBQ0xDLENBQUFBLEdBQUFBLFlBQUFBLE1BQU07UUFDUjtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHTCxPQUFNTSxRQUFRLENBQXdCO0lBQ3BFLE1BQU1DLFlBQVlQLE9BQU1RLFdBQVcsQ0FBQyxDQUFDQztRQUNuQ0osV0FBV0k7SUFDYixHQUFHLEVBQUU7SUFFTFQsT0FBTUMsU0FBUyxDQUFDO1FBQ2QsSUFBSUcsV0FBVyxNQUFNO1lBQ25CO1FBQ0Y7UUFFQSxNQUFNTSxVQUFVQyxDQUFBQSxHQUFBQSxrQkFBQUEsT0FBUSxFQUFDO1lBQUVDLFNBQVNSO1FBQVE7UUFDNUMsT0FBTztZQUNMTSxRQUFRRyxTQUFTO1FBQ25CO0lBQ0YsR0FBRztRQUFDVDtLQUFRO0lBRVosT0FDRSxXQURGLEdBQ0UsSUFBQVUsWUFBQUMsSUFBQSxFQUFDQyxPQUFBQTtRQUFJQyw4QkFBMEI7UUFBQ3BCLFdBQVdBO1FBQVdxQixLQUFLWDs7MEJBQ3pELElBQUFPLFlBQUFLLEdBQUEsRUFBQ0gsT0FBQUE7Z0JBQ0NJLCtCQUEyQjtnQkFDM0JDLHFDQUFtQ3RCLFFBQVEsT0FBT3VCOztZQUVuRHhCOzs7QUFHUDtHQXJDTUg7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9jb21wb25lbnRzL092ZXJsYXkvT3ZlcmxheS50c3g/MWU5YiJdLCJuYW1lcyI6WyJPdmVybGF5IiwicGFyYW0iLCJjbGFzc05hbWUiLCJjaGlsZHJlbiIsImZpeGVkIiwiUmVhY3QiLCJ1c2VFZmZlY3QiLCJsb2NrIiwidW5sb2NrIiwib3ZlcmxheSIsInNldE92ZXJsYXkiLCJ1c2VTdGF0ZSIsIm9uT3ZlcmxheSIsInVzZUNhbGxiYWNrIiwiZWwiLCJoYW5kbGUyIiwiYWxseVRyYXAiLCJjb250ZXh0IiwiZGlzZW5nYWdlIiwiX2pzeHJ1bnRpbWUiLCJqc3hzIiwiZGl2IiwiZGF0YS1uZXh0anMtZGlhbG9nLW92ZXJsYXkiLCJyZWYiLCJqc3giLCJkYXRhLW5leHRqcy1kaWFsb2ctYmFja2Ryb3AiLCJkYXRhLW5leHRqcy1kaWFsb2ctYmFja2Ryb3AtZml4ZWQiLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/Overlay.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/body-locker.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/body-locker.js ***!
  \***************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    lock: function() {\n        return lock;\n    },\n    unlock: function() {\n        return unlock;\n    }\n});\nlet previousBodyPaddingRight;\nlet previousBodyOverflowSetting;\nlet activeLocks = 0;\nfunction lock() {\n    setTimeout(()=>{\n        if (activeLocks++ > 0) {\n            return;\n        }\n        const scrollBarGap = window.innerWidth - document.documentElement.clientWidth;\n        if (scrollBarGap > 0) {\n            previousBodyPaddingRight = document.body.style.paddingRight;\n            document.body.style.paddingRight = \"\" + scrollBarGap + \"px\";\n        }\n        previousBodyOverflowSetting = document.body.style.overflow;\n        document.body.style.overflow = \"hidden\";\n    });\n}\nfunction unlock() {\n    setTimeout(()=>{\n        if (activeLocks === 0 || --activeLocks !== 0) {\n            return;\n        }\n        if (previousBodyPaddingRight !== undefined) {\n            document.body.style.paddingRight = previousBodyPaddingRight;\n            previousBodyPaddingRight = undefined;\n        }\n        if (previousBodyOverflowSetting !== undefined) {\n            document.body.style.overflow = previousBodyOverflowSetting;\n            previousBodyOverflowSetting = undefined;\n        }\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=body-locker.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvT3ZlcmxheS9ib2R5LWxvY2tlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFLZ0JBLE1BQUk7ZUFBSkE7O0lBbUJBQyxRQUFNO2VBQU5BOzs7QUF4QmhCLElBQUlDO0FBQ0osSUFBSUM7QUFFSixJQUFJQyxjQUFjO0FBRVgsU0FBU0o7SUFDZEssV0FBVztRQUNULElBQUlELGdCQUFnQixHQUFHO1lBQ3JCO1FBQ0Y7UUFFQSxNQUFNRSxlQUNKQyxPQUFPQyxVQUFVLEdBQUdDLFNBQVNDLGVBQWUsQ0FBQ0MsV0FBVztRQUUxRCxJQUFJTCxlQUFlLEdBQUc7WUFDcEJKLDJCQUEyQk8sU0FBU0csSUFBSSxDQUFDQyxLQUFLLENBQUNDLFlBQVk7WUFDM0RMLFNBQVNHLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxZQUFZLEdBQUcsS0FBR1IsZUFBYTtRQUNyRDtRQUVBSCw4QkFBOEJNLFNBQVNHLElBQUksQ0FBQ0MsS0FBSyxDQUFDRSxRQUFRO1FBQzFETixTQUFTRyxJQUFJLENBQUNDLEtBQUssQ0FBQ0UsUUFBUSxHQUFHO0lBQ2pDO0FBQ0Y7QUFFTyxTQUFTZDtJQUNkSSxXQUFXO1FBQ1QsSUFBSUQsZ0JBQWdCLEtBQUssRUFBRUEsZ0JBQWdCLEdBQUc7WUFDNUM7UUFDRjtRQUVBLElBQUlGLDZCQUE2QmMsV0FBVztZQUMxQ1AsU0FBU0csSUFBSSxDQUFDQyxLQUFLLENBQUNDLFlBQVksR0FBR1o7WUFDbkNBLDJCQUEyQmM7UUFDN0I7UUFFQSxJQUFJYixnQ0FBZ0NhLFdBQVc7WUFDN0NQLFNBQVNHLElBQUksQ0FBQ0MsS0FBSyxDQUFDRSxRQUFRLEdBQUdaO1lBQy9CQSw4QkFBOEJhO1FBQ2hDO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvT3ZlcmxheS9ib2R5LWxvY2tlci50cz9mOTc1Il0sIm5hbWVzIjpbImxvY2siLCJ1bmxvY2siLCJwcmV2aW91c0JvZHlQYWRkaW5nUmlnaHQiLCJwcmV2aW91c0JvZHlPdmVyZmxvd1NldHRpbmciLCJhY3RpdmVMb2NrcyIsInNldFRpbWVvdXQiLCJzY3JvbGxCYXJHYXAiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiZG9jdW1lbnQiLCJkb2N1bWVudEVsZW1lbnQiLCJjbGllbnRXaWR0aCIsImJvZHkiLCJzdHlsZSIsInBhZGRpbmdSaWdodCIsIm92ZXJmbG93IiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/body-locker.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/index.js ***!
  \*********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Overlay\", ({\n    enumerable: true,\n    get: function() {\n        return _Overlay.Overlay;\n    }\n}));\nconst _Overlay = __webpack_require__(/*! ./Overlay */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/Overlay.js\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvT3ZlcmxheS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OzJDQUFTQTs7O2VBQUFBLFNBQUFBLE9BQU87OztxQ0FBUSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvT3ZlcmxheS9pbmRleC50c3g/OTM4YyJdLCJuYW1lcyI6WyJPdmVybGF5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/index.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/styles.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/styles.js ***!
  \**********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _nooptemplate = __webpack_require__(/*! ../../helpers/noop-template */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  [data-nextjs-dialog-overlay] {\\n    position: fixed;\\n    top: 0;\\n    right: 0;\\n    bottom: 0;\\n    left: 0;\\n    overflow: auto;\\n    z-index: 9000;\\n\\n    display: flex;\\n    align-content: center;\\n    align-items: center;\\n    flex-direction: column;\\n    padding: 10vh 15px 0;\\n  }\\n\\n  @media (max-height: 812px) {\\n    [data-nextjs-dialog-overlay] {\\n      padding: 15px 15px 0;\\n    }\\n  }\\n\\n  [data-nextjs-dialog-backdrop] {\\n    position: fixed;\\n    top: 0;\\n    right: 0;\\n    bottom: 0;\\n    left: 0;\\n    background-color: var(--color-backdrop);\\n    pointer-events: all;\\n    z-index: -1;\\n  }\\n\\n  [data-nextjs-dialog-backdrop-fixed] {\\n    cursor: not-allowed;\\n    -webkit-backdrop-filter: blur(8px);\\n    backdrop-filter: blur(8px);\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvT3ZlcmxheS9zdHlsZXMuanMiLCJtYXBwaW5ncyI6Ijs7OzswQ0EyQ1NBOzs7ZUFBQUE7Ozs7MENBM0NtQjs7Ozs7Ozs7OztBQUU1QixNQUFNQSxTQUFBQSxDQUFBQSxHQUFTQyxjQUFBQSxJQUFHLEVBQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9PdmVybGF5L3N0eWxlcy50c3g/MWQ4MiJdLCJuYW1lcyI6WyJzdHlsZXMiLCJjc3MiLCJfdGVtcGxhdGVPYmplY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/styles.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/ShadowPortal.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/ShadowPortal.js ***!
  \********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ShadowPortal\", ({\n    enumerable: true,\n    get: function() {\n        return ShadowPortal;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _reactdom = __webpack_require__(/*! react-dom */ \"./node_modules/react-dom/index.js\");\nfunction ShadowPortal(param) {\n    _s();\n    let { children } = param;\n    let portalNode = _react.useRef(null);\n    let shadowNode = _react.useRef(null);\n    let [, forceUpdate] = _react.useState();\n    _react.useLayoutEffect(()=>{\n        const ownerDocument = document;\n        portalNode.current = ownerDocument.createElement(\"nextjs-portal\");\n        shadowNode.current = portalNode.current.attachShadow({\n            mode: \"open\"\n        });\n        ownerDocument.body.appendChild(portalNode.current);\n        forceUpdate({});\n        return ()=>{\n            if (portalNode.current && portalNode.current.ownerDocument) {\n                portalNode.current.ownerDocument.body.removeChild(portalNode.current);\n            }\n        };\n    }, []);\n    return shadowNode.current ? /*#__PURE__*/ (0, _reactdom.createPortal)(children, shadowNode.current) : null;\n}\n_s(ShadowPortal, \"P7YL0rn/sjH62F7+OsEXN5GMw3U=\");\n_c = ShadowPortal;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=ShadowPortal.js.map\nvar _c;\n$RefreshReg$(_c, \"ShadowPortal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvU2hhZG93UG9ydGFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztnREFHZ0JBOzs7ZUFBQUE7Ozs7NkVBSE87c0NBQ007QUFFdEIsU0FBU0EsYUFBYUMsS0FBMkM7O0lBQTNDLE1BQUVDLFFBQVEsRUFBaUMsR0FBM0NEO0lBQzNCLElBQUlFLGFBQWFDLE9BQU1DLE1BQU0sQ0FBcUI7SUFDbEQsSUFBSUMsYUFBYUYsT0FBTUMsTUFBTSxDQUFvQjtJQUNqRCxJQUFJLEdBQUdFLFlBQVksR0FBR0gsT0FBTUksUUFBUTtJQUVwQ0osT0FBTUssZUFBZSxDQUFDO1FBQ3BCLE1BQU1DLGdCQUFnQkM7UUFDdEJSLFdBQVdTLE9BQU8sR0FBR0YsY0FBY0csYUFBYSxDQUFDO1FBQ2pEUCxXQUFXTSxPQUFPLEdBQUdULFdBQVdTLE9BQU8sQ0FBQ0UsWUFBWSxDQUFDO1lBQUVDLE1BQU07UUFBTztRQUNwRUwsY0FBY00sSUFBSSxDQUFDQyxXQUFXLENBQUNkLFdBQVdTLE9BQU87UUFDakRMLFlBQVksQ0FBQztRQUNiLE9BQU87WUFDTCxJQUFJSixXQUFXUyxPQUFPLElBQUlULFdBQVdTLE9BQU8sQ0FBQ0YsYUFBYSxFQUFFO2dCQUMxRFAsV0FBV1MsT0FBTyxDQUFDRixhQUFhLENBQUNNLElBQUksQ0FBQ0UsV0FBVyxDQUFDZixXQUFXUyxPQUFPO1lBQ3RFO1FBQ0Y7SUFDRixHQUFHLEVBQUU7SUFFTCxPQUFPTixXQUFXTSxPQUFPLEdBQ3JCTyxXQURxQixHQUNyQkEsQ0FBQUEsR0FBQUEsVUFBQUEsWUFBWSxFQUFDakIsVUFBVUksV0FBV00sT0FBTyxJQUN6QztBQUNOO0dBckJnQlo7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9jb21wb25lbnRzL1NoYWRvd1BvcnRhbC50c3g/ZGM4OSJdLCJuYW1lcyI6WyJTaGFkb3dQb3J0YWwiLCJwYXJhbSIsImNoaWxkcmVuIiwicG9ydGFsTm9kZSIsIlJlYWN0IiwidXNlUmVmIiwic2hhZG93Tm9kZSIsImZvcmNlVXBkYXRlIiwidXNlU3RhdGUiLCJ1c2VMYXlvdXRFZmZlY3QiLCJvd25lckRvY3VtZW50IiwiZG9jdW1lbnQiLCJjdXJyZW50IiwiY3JlYXRlRWxlbWVudCIsImF0dGFjaFNoYWRvdyIsIm1vZGUiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJyZW1vdmVDaGlsZCIsImNyZWF0ZVBvcnRhbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/ShadowPortal.js\n"));

/***/ })

});