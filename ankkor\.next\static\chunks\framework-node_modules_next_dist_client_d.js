"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_d"],{

/***/ "./node_modules/next/dist/client/detect-domain-locale.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/client/detect-domain-locale.js ***!
  \***************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"detectDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return detectDomainLocale;\n    }\n}));\nconst detectDomainLocale = function() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    if (false) {}\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=detect-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9kZXRlY3QtZG9tYWluLWxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O3NEQUVhQTs7O2VBQUFBOzs7QUFBTixNQUFNQSxxQkFBZ0M7cUNBQUlDLE9BQUFBLElBQUFBLE1BQUFBLE9BQUFBLE9BQUFBLEdBQUFBLE9BQUFBLE1BQUFBLE9BQUFBO1FBQUFBLElBQUFBLENBQUFBLEtBQUFBLEdBQUFBLFNBQUFBLENBQUFBLEtBQUFBOztJQUMvQyxJQUFJQyxLQUErQixFQUFFLEVBSXJDO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvZGV0ZWN0LWRvbWFpbi1sb2NhbGUudHM/NjhmYyJdLCJuYW1lcyI6WyJkZXRlY3REb21haW5Mb2NhbGUiLCJhcmdzIiwicHJvY2VzcyIsImVudiIsIl9fTkVYVF9JMThOX1NVUFBPUlQiLCJyZXF1aXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/detect-domain-locale.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/dev/dev-build-watcher.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-watcher.js ***!
  \****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* eslint-disable @typescript-eslint/no-use-before-define */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return initializeBuildWatcher;\n    }\n}));\nconst _hotreloadertypes = __webpack_require__(/*! ../../server/dev/hot-reloader-types */ \"./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _websocket = __webpack_require__(/*! ../components/react-dev-overlay/pages/websocket */ \"./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\");\nfunction initializeBuildWatcher(toggleCallback, position) {\n    if (position === void 0) position = \"bottom-right\";\n    const shadowHost = document.createElement(\"div\");\n    const [verticalProperty, horizontalProperty] = position.split(\"-\", 2);\n    shadowHost.id = \"__next-build-watcher\";\n    // Make sure container is fixed and on a high zIndex so it shows\n    shadowHost.style.position = \"fixed\";\n    // Ensure container's position to be top or bottom (default)\n    shadowHost.style[verticalProperty] = \"10px\";\n    // Ensure container's position to be left or right (default)\n    shadowHost.style[horizontalProperty] = \"20px\";\n    shadowHost.style.width = \"0\";\n    shadowHost.style.height = \"0\";\n    shadowHost.style.zIndex = \"99999\";\n    document.body.appendChild(shadowHost);\n    let shadowRoot;\n    let prefix = \"\";\n    if (shadowHost.attachShadow) {\n        shadowRoot = shadowHost.attachShadow({\n            mode: \"open\"\n        });\n    } else {\n        // If attachShadow is undefined then the browser does not support\n        // the Shadow DOM, we need to prefix all the names so there\n        // will be no conflicts\n        shadowRoot = shadowHost;\n        prefix = \"__next-build-watcher-\";\n    }\n    // Container\n    const container = createContainer(prefix);\n    shadowRoot.appendChild(container);\n    // CSS\n    const css = createCss(prefix, {\n        horizontalProperty,\n        verticalProperty\n    });\n    shadowRoot.appendChild(css);\n    // State\n    let isVisible = false;\n    let isBuilding = false;\n    let timeoutId = null;\n    // Handle events\n    (0, _websocket.addMessageListener)((obj)=>{\n        try {\n            handleMessage(obj);\n        } catch (e) {}\n    });\n    function show() {\n        timeoutId && clearTimeout(timeoutId);\n        isVisible = true;\n        isBuilding = true;\n        updateContainer();\n    }\n    function hide() {\n        isBuilding = false;\n        // Wait for the fade out transition to complete\n        timeoutId = setTimeout(()=>{\n            isVisible = false;\n            updateContainer();\n        }, 100);\n        updateContainer();\n    }\n    function handleMessage(obj) {\n        if (!(\"action\" in obj)) {\n            return;\n        }\n        // eslint-disable-next-line default-case\n        switch(obj.action){\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n                show();\n                break;\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n                hide();\n                break;\n        }\n    }\n    toggleCallback({\n        show,\n        hide\n    });\n    function updateContainer() {\n        if (isBuilding) {\n            container.classList.add(\"\" + prefix + \"building\");\n        } else {\n            container.classList.remove(\"\" + prefix + \"building\");\n        }\n        if (isVisible) {\n            container.classList.add(\"\" + prefix + \"visible\");\n        } else {\n            container.classList.remove(\"\" + prefix + \"visible\");\n        }\n    }\n}\nfunction createContainer(prefix) {\n    const container = document.createElement(\"div\");\n    container.id = \"\" + prefix + \"container\";\n    container.innerHTML = '\\n    <div id=\"' + prefix + 'icon-wrapper\">\\n      <svg viewBox=\"0 0 226 200\">\\n        <defs>\\n          <linearGradient\\n            x1=\"114.720775%\"\\n            y1=\"181.283245%\"\\n            x2=\"39.5399306%\"\\n            y2=\"100%\"\\n            id=\"' + prefix + 'linear-gradient\"\\n          >\\n            <stop stop-color=\"#000000\" offset=\"0%\" />\\n            <stop stop-color=\"#FFFFFF\" offset=\"100%\" />\\n          </linearGradient>\\n        </defs>\\n        <g id=\"' + prefix + 'icon-group\" fill=\"none\" stroke=\"url(#' + prefix + 'linear-gradient)\" stroke-width=\"18\">\\n          <path d=\"M113,5.08219117 L4.28393801,197.5 L221.716062,197.5 L113,5.08219117 Z\" />\\n        </g>\\n      </svg>\\n    </div>\\n  ';\n    return container;\n}\nfunction createCss(prefix, param) {\n    let { horizontalProperty, verticalProperty } = param;\n    const css = document.createElement(\"style\");\n    css.textContent = \"\\n    #\" + prefix + \"container {\\n      position: absolute;\\n      \" + verticalProperty + \": 10px;\\n      \" + horizontalProperty + \": 30px;\\n\\n      border-radius: 3px;\\n      background: #000;\\n      color: #fff;\\n      font: initial;\\n      cursor: initial;\\n      letter-spacing: initial;\\n      text-shadow: initial;\\n      text-transform: initial;\\n      visibility: initial;\\n\\n      padding: 7px 10px 8px 10px;\\n      align-items: center;\\n      box-shadow: 0 11px 40px 0 rgba(0, 0, 0, 0.25), 0 2px 10px 0 rgba(0, 0, 0, 0.12);\\n\\n      display: none;\\n      opacity: 0;\\n      transition: opacity 0.1s ease, \" + verticalProperty + \" 0.1s ease;\\n      animation: \" + prefix + \"fade-in 0.1s ease-in-out;\\n    }\\n\\n    #\" + prefix + \"container.\" + prefix + \"visible {\\n      display: flex;\\n    }\\n\\n    #\" + prefix + \"container.\" + prefix + \"building {\\n      \" + verticalProperty + \": 20px;\\n      opacity: 1;\\n    }\\n\\n    #\" + prefix + \"icon-wrapper {\\n      width: 16px;\\n      height: 16px;\\n    }\\n\\n    #\" + prefix + \"icon-wrapper > svg {\\n      width: 100%;\\n      height: 100%;\\n    }\\n\\n    #\" + prefix + \"icon-group {\\n      animation: \" + prefix + \"strokedash 1s ease-in-out both infinite;\\n    }\\n\\n    @keyframes \" + prefix + \"fade-in {\\n      from {\\n        \" + verticalProperty + \": 10px;\\n        opacity: 0;\\n      }\\n      to {\\n        \" + verticalProperty + \": 20px;\\n        opacity: 1;\\n      }\\n    }\\n\\n    @keyframes \" + prefix + \"strokedash {\\n      0% {\\n        stroke-dasharray: 0 226;\\n      }\\n      80%,\\n      100% {\\n        stroke-dasharray: 659 226;\\n      }\\n    }\\n  \";\n    return css;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-build-watcher.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9kZXYvZGV2LWJ1aWxkLXdhdGNoZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsMERBQTBEOzs7OzJDQWExRDs7O2VBQXdCQTs7OzhDQVpvQjt1Q0FFVDtBQVVwQixTQUFTQSx1QkFDdEJDLGNBQW1ELEVBQ25EQyxRQUF5QjtJQUF6QkEsSUFBQUEsYUFBQUEsS0FBQUEsR0FBQUEsV0FBVztJQUVYLE1BQU1DLGFBQWFDLFNBQVNDLGFBQWEsQ0FBQztJQUMxQyxNQUFNLENBQUNDLGtCQUFrQkMsbUJBQW1CLEdBQUdMLFNBQVNNLEtBQUssQ0FBQyxLQUFLO0lBSW5FTCxXQUFXTSxFQUFFLEdBQUc7SUFDaEIsZ0VBQWdFO0lBQ2hFTixXQUFXTyxLQUFLLENBQUNSLFFBQVEsR0FBRztJQUM1Qiw0REFBNEQ7SUFDNURDLFdBQVdPLEtBQUssQ0FBQ0osaUJBQWlCLEdBQUc7SUFDckMsNERBQTREO0lBQzVESCxXQUFXTyxLQUFLLENBQUNILG1CQUFtQixHQUFHO0lBQ3ZDSixXQUFXTyxLQUFLLENBQUNDLEtBQUssR0FBRztJQUN6QlIsV0FBV08sS0FBSyxDQUFDRSxNQUFNLEdBQUc7SUFDMUJULFdBQVdPLEtBQUssQ0FBQ0csTUFBTSxHQUFHO0lBQzFCVCxTQUFTVSxJQUFJLENBQUNDLFdBQVcsQ0FBQ1o7SUFFMUIsSUFBSWE7SUFDSixJQUFJQyxTQUFTO0lBRWIsSUFBSWQsV0FBV2UsWUFBWSxFQUFFO1FBQzNCRixhQUFhYixXQUFXZSxZQUFZLENBQUM7WUFBRUMsTUFBTTtRQUFPO0lBQ3RELE9BQU87UUFDTCxpRUFBaUU7UUFDakUsMkRBQTJEO1FBQzNELHVCQUF1QjtRQUN2QkgsYUFBYWI7UUFDYmMsU0FBUztJQUNYO0lBRUEsWUFBWTtJQUNaLE1BQU1HLFlBQVlDLGdCQUFnQko7SUFDbENELFdBQVdELFdBQVcsQ0FBQ0s7SUFFdkIsTUFBTTtJQUNOLE1BQU1FLE1BQU1DLFVBQVVOLFFBQVE7UUFBRVY7UUFBb0JEO0lBQWlCO0lBQ3JFVSxXQUFXRCxXQUFXLENBQUNPO0lBRXZCLFFBQVE7SUFDUixJQUFJRSxZQUFZO0lBQ2hCLElBQUlDLGFBQWE7SUFDakIsSUFBSUMsWUFBa0Q7SUFFdEQsZ0JBQWdCO0lBRWhCQyxDQUFBQSxHQUFBQSxXQUFBQSxrQkFBa0IsRUFBQyxDQUFDQztRQUNsQixJQUFJO1lBQ0ZDLGNBQWNEO1FBQ2hCLEVBQUUsT0FBQUUsR0FBTSxDQUFDO0lBQ1g7SUFFQSxTQUFTQztRQUNQTCxhQUFhTSxhQUFhTjtRQUMxQkYsWUFBWTtRQUNaQyxhQUFhO1FBQ2JRO0lBQ0Y7SUFFQSxTQUFTQztRQUNQVCxhQUFhO1FBQ2IsK0NBQStDO1FBQy9DQyxZQUFZUyxXQUFXO1lBQ3JCWCxZQUFZO1lBQ1pTO1FBQ0YsR0FBRztRQUNIQTtJQUNGO0lBRUEsU0FBU0osY0FBY0QsR0FBcUI7UUFDMUMsSUFBSSxDQUFFLGFBQVlBLEdBQUFBLEdBQU07WUFDdEI7UUFDRjtRQUVBLHdDQUF3QztRQUN4QyxPQUFRQSxJQUFJUSxNQUFNO1lBQ2hCLEtBQUtDLGtCQUFBQSwyQkFBMkIsQ0FBQ0MsUUFBUTtnQkFDdkNQO2dCQUNBO1lBQ0YsS0FBS00sa0JBQUFBLDJCQUEyQixDQUFDRSxLQUFLO1lBQ3RDLEtBQUtGLGtCQUFBQSwyQkFBMkIsQ0FBQ0csSUFBSTtnQkFDbkNOO2dCQUNBO1FBQ0o7SUFDRjtJQUVBakMsZUFBZTtRQUNiOEI7UUFDQUc7SUFDRjtJQUVBLFNBQVNEO1FBQ1AsSUFBSVIsWUFBWTtZQUNkTCxVQUFVcUIsU0FBUyxDQUFDQyxHQUFHLENBQUMsS0FBR3pCLFNBQU87UUFDcEMsT0FBTztZQUNMRyxVQUFVcUIsU0FBUyxDQUFDRSxNQUFNLENBQUMsS0FBRzFCLFNBQU87UUFDdkM7UUFFQSxJQUFJTyxXQUFXO1lBQ2JKLFVBQVVxQixTQUFTLENBQUNDLEdBQUcsQ0FBQyxLQUFHekIsU0FBTztRQUNwQyxPQUFPO1lBQ0xHLFVBQVVxQixTQUFTLENBQUNFLE1BQU0sQ0FBQyxLQUFHMUIsU0FBTztRQUN2QztJQUNGO0FBQ0Y7QUFFQSxTQUFTSSxnQkFBZ0JKLE1BQWM7SUFDckMsTUFBTUcsWUFBWWhCLFNBQVNDLGFBQWEsQ0FBQztJQUN6Q2UsVUFBVVgsRUFBRSxHQUFHLEtBQUdRLFNBQU87SUFDekJHLFVBQVV3QixTQUFTLEdBQUcsb0JBQ1QzQixTQUFPLG9PQVFKQSxTQUFPLGlOQU1SQSxTQUFPLDBDQUF1Q0EsU0FBTztJQU9wRSxPQUFPRztBQUNUO0FBRUEsU0FBU0csVUFDUE4sTUFBYyxFQUNkNEIsS0FHMkQ7SUFIM0QsTUFDRXRDLGtCQUFrQixFQUNsQkQsZ0JBQWdCLEVBQ3lDLEdBSDNEdUM7SUFLQSxNQUFNdkIsTUFBTWxCLFNBQVNDLGFBQWEsQ0FBQztJQUNuQ2lCLElBQUl3QixXQUFXLEdBQUcsWUFDYjdCLFNBQU8sbURBRU5YLG1CQUFpQixvQkFDakJDLHFCQUFtQix3ZUFrQllELG1CQUFpQixtQ0FDckNXLFNBQU8sOENBR25CQSxTQUFPLGVBQVlBLFNBQU8sb0RBSTFCQSxTQUFPLGVBQVlBLFNBQU8sdUJBQ3pCWCxtQkFBaUIsK0NBSWxCVyxTQUFPLDRFQUtQQSxTQUFPLGtGQUtQQSxTQUFPLG9DQUNLQSxTQUFPLHVFQUdUQSxTQUFPLHNDQUVkWCxtQkFBaUIsZ0VBSWpCQSxtQkFBaUIsb0VBS1ZXLFNBQU87SUFXdEIsT0FBT0s7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9kZXYvZGV2LWJ1aWxkLXdhdGNoZXIudHM/YzQ4OSJdLCJuYW1lcyI6WyJpbml0aWFsaXplQnVpbGRXYXRjaGVyIiwidG9nZ2xlQ2FsbGJhY2siLCJwb3NpdGlvbiIsInNoYWRvd0hvc3QiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJ2ZXJ0aWNhbFByb3BlcnR5IiwiaG9yaXpvbnRhbFByb3BlcnR5Iiwic3BsaXQiLCJpZCIsInN0eWxlIiwid2lkdGgiLCJoZWlnaHQiLCJ6SW5kZXgiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJzaGFkb3dSb290IiwicHJlZml4IiwiYXR0YWNoU2hhZG93IiwibW9kZSIsImNvbnRhaW5lciIsImNyZWF0ZUNvbnRhaW5lciIsImNzcyIsImNyZWF0ZUNzcyIsImlzVmlzaWJsZSIsImlzQnVpbGRpbmciLCJ0aW1lb3V0SWQiLCJhZGRNZXNzYWdlTGlzdGVuZXIiLCJvYmoiLCJoYW5kbGVNZXNzYWdlIiwiZSIsInNob3ciLCJjbGVhclRpbWVvdXQiLCJ1cGRhdGVDb250YWluZXIiLCJoaWRlIiwic2V0VGltZW91dCIsImFjdGlvbiIsIkhNUl9BQ1RJT05TX1NFTlRfVE9fQlJPV1NFUiIsIkJVSUxESU5HIiwiQlVJTFQiLCJTWU5DIiwiY2xhc3NMaXN0IiwiYWRkIiwicmVtb3ZlIiwiaW5uZXJIVE1MIiwicGFyYW0iLCJ0ZXh0Q29udGVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/dev/dev-build-watcher.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/dev/fouc.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/client/dev/fouc.js ***!
  \***************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// This wrapper function is used to safely select the best available function\n// to schedule removal of the no-FOUC styles workaround. requestAnimationFrame\n// is the ideal choice, but when used in iframes, there are no guarantees that\n// the callback will actually be called, which could stall the promise returned\n// from displayContent.\n//\n// See: https://www.vector-logic.com/blog/posts/on-request-animation-frame-and-embedded-iframes\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"displayContent\", ({\n    enumerable: true,\n    get: function() {\n        return displayContent;\n    }\n}));\nconst safeCallbackQueue = (callback)=>{\n    if (window.requestAnimationFrame && window.self === window.top) {\n        window.requestAnimationFrame(callback);\n    } else {\n        window.setTimeout(callback);\n    }\n};\nfunction displayContent() {\n    return new Promise((resolve)=>{\n        safeCallbackQueue(function() {\n            for(var x = document.querySelectorAll(\"[data-next-hide-fouc]\"), i = x.length; i--;){\n                x[i].parentNode.removeChild(x[i]);\n            }\n            resolve();\n        });\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=fouc.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9kZXYvZm91Yy5qcyIsIm1hcHBpbmdzIjoiQUFBQSw2RUFBNkU7QUFDN0UsOEVBQThFO0FBQzlFLDhFQUE4RTtBQUM5RSwrRUFBK0U7QUFDL0UsdUJBQXVCO0FBQ3ZCLEVBQUU7QUFDRiwrRkFBK0Y7Ozs7O2tEQVkvRUE7OztlQUFBQTs7O0FBWGhCLE1BQU1DLG9CQUFvQixDQUFDQztJQUN6QixJQUFJQyxPQUFPQyxxQkFBcUIsSUFBSUQsT0FBT0UsSUFBSSxLQUFLRixPQUFPRyxHQUFHLEVBQUU7UUFDOURILE9BQU9DLHFCQUFxQixDQUFDRjtJQUMvQixPQUFPO1FBQ0xDLE9BQU9JLFVBQVUsQ0FBQ0w7SUFDcEI7QUFDRjtBQUtPLFNBQVNGO0lBQ2QsT0FBTyxJQUFJUSxRQUFRLENBQUNDO1FBQ2xCUixrQkFBa0I7WUFDaEIsSUFDRSxJQUFJUyxJQUFJQyxTQUFTQyxnQkFBZ0IsQ0FBQywwQkFDaENDLElBQUlILEVBQUVJLE1BQU0sRUFDZEQsS0FFQTtnQkFDQUgsQ0FBQyxDQUFDRyxFQUFFLENBQUNFLFVBQVUsQ0FBRUMsV0FBVyxDQUFDTixDQUFDLENBQUNHLEVBQUU7WUFDbkM7WUFDQUo7UUFDRjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvZGV2L2ZvdWMudHM/MDEyYyJdLCJuYW1lcyI6WyJkaXNwbGF5Q29udGVudCIsInNhZmVDYWxsYmFja1F1ZXVlIiwiY2FsbGJhY2siLCJ3aW5kb3ciLCJyZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJzZWxmIiwidG9wIiwic2V0VGltZW91dCIsIlByb21pc2UiLCJyZXNvbHZlIiwieCIsImRvY3VtZW50IiwicXVlcnlTZWxlY3RvckFsbCIsImkiLCJsZW5ndGgiLCJwYXJlbnROb2RlIiwicmVtb3ZlQ2hpbGQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/dev/fouc.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/dev/hot-middleware-client.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/hot-middleware-client.js ***!
  \********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _hotreloaderclient = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../components/react-dev-overlay/pages/hot-reloader-client */ \"./node_modules/next/dist/client/components/react-dev-overlay/pages/hot-reloader-client.js\"));\nconst _websocket = __webpack_require__(/*! ../components/react-dev-overlay/pages/websocket */ \"./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\");\nlet reloading = false;\nconst _default = (mode)=>{\n    const devClient = (0, _hotreloaderclient.default)(mode);\n    devClient.subscribeToHmrEvent((obj)=>{\n        if (reloading) return;\n        // if we're on an error/404 page, we can't reliably tell if the newly added/removed page\n        // matches the current path. In that case, assume any added/removed entries should trigger a reload of the current page\n        const isOnErrorPage = window.next.router.pathname === \"/404\" || window.next.router.pathname === \"/_error\";\n        switch(obj.action){\n            case \"reloadPage\":\n                {\n                    (0, _websocket.sendMessage)(JSON.stringify({\n                        event: \"client-reload-page\",\n                        clientId: window.__nextDevClientId\n                    }));\n                    reloading = true;\n                    return window.location.reload();\n                }\n            case \"removedPage\":\n                {\n                    const [page] = obj.data;\n                    if (page === window.next.router.pathname || isOnErrorPage) {\n                        (0, _websocket.sendMessage)(JSON.stringify({\n                            event: \"client-removed-page\",\n                            clientId: window.__nextDevClientId,\n                            page\n                        }));\n                        return window.location.reload();\n                    }\n                    return;\n                }\n            case \"addedPage\":\n                {\n                    const [page] = obj.data;\n                    if (page === window.next.router.pathname && typeof window.next.router.components[page] === \"undefined\" || isOnErrorPage) {\n                        (0, _websocket.sendMessage)(JSON.stringify({\n                            event: \"client-added-page\",\n                            clientId: window.__nextDevClientId,\n                            page\n                        }));\n                        return window.location.reload();\n                    }\n                    return;\n                }\n            case \"serverError\":\n            case \"devPagesManifestUpdate\":\n            case \"building\":\n            case \"finishBuilding\":\n                {\n                    return;\n                }\n            default:\n                {\n                    throw new Error(\"Unexpected action \" + obj.action);\n                }\n        }\n    });\n    return devClient;\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hot-middleware-client.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/dev/hot-middleware-client.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/dev/on-demand-entries-client.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/on-demand-entries-client.js ***!
  \***********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _router = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../router */ \"./node_modules/next/dist/client/router.js\"));\nconst _websocket = __webpack_require__(/*! ../components/react-dev-overlay/pages/websocket */ \"./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\");\nconst _default = async (page)=>{\n    if (page) {\n        // in AMP the router isn't initialized on the client and\n        // client-transitions don't occur so ping initial page\n        setInterval(()=>{\n            (0, _websocket.sendMessage)(JSON.stringify({\n                event: \"ping\",\n                page\n            }));\n        }, 2500);\n    } else {\n        _router.default.ready(()=>{\n            setInterval(()=>{\n                // when notFound: true is returned we should use the notFoundPage\n                // as the Router.pathname will point to the 404 page but we want\n                // to ping the source page that returned notFound: true instead\n                const notFoundSrcPage = self.__NEXT_DATA__.notFoundSrcPage;\n                const pathname = (_router.default.pathname === \"/404\" || _router.default.pathname === \"/_error\") && notFoundSrcPage ? notFoundSrcPage : _router.default.pathname;\n                (0, _websocket.sendMessage)(JSON.stringify({\n                    event: \"ping\",\n                    page: pathname\n                }));\n            }, 2500);\n        });\n    }\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=on-demand-entries-client.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9kZXYvb24tZGVtYW5kLWVudHJpZXMtY2xpZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7MkNBR0E7OztlQUFBQTs7Ozs2RUFIbUI7dUNBQ1M7TUFFNUJBLFdBQWUsT0FBT0M7SUFDcEIsSUFBSUEsTUFBTTtRQUNSLHdEQUF3RDtRQUN4RCxzREFBc0Q7UUFDdERDLFlBQVk7WUFDVkMsQ0FBQUEsR0FBQUEsV0FBQUEsV0FBVyxFQUFDQyxLQUFLQyxTQUFTLENBQUM7Z0JBQUVDLE9BQU87Z0JBQVFMO1lBQUs7UUFDbkQsR0FBRztJQUNMLE9BQU87UUFDTE0sUUFBQUEsT0FBTSxDQUFDQyxLQUFLLENBQUM7WUFDWE4sWUFBWTtnQkFDVixpRUFBaUU7Z0JBQ2pFLGdFQUFnRTtnQkFDaEUsK0RBQStEO2dCQUMvRCxNQUFNTyxrQkFBa0JDLEtBQUtDLGFBQWEsQ0FBQ0YsZUFBZTtnQkFDMUQsTUFBTUcsV0FDSixDQUFDTCxRQUFBQSxPQUFNLENBQUNLLFFBQVEsS0FBSyxVQUFVTCxRQUFBQSxPQUFNLENBQUNLLFFBQVEsS0FBSyxjQUNuREgsa0JBQ0lBLGtCQUNBRixRQUFBQSxPQUFNLENBQUNLLFFBQVE7Z0JBRXJCVCxDQUFBQSxHQUFBQSxXQUFBQSxXQUFXLEVBQUNDLEtBQUtDLFNBQVMsQ0FBQztvQkFBRUMsT0FBTztvQkFBUUwsTUFBTVc7Z0JBQVM7WUFDN0QsR0FBRztRQUNMO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9kZXYvb24tZGVtYW5kLWVudHJpZXMtY2xpZW50LnRzPzQ0YTYiXSwibmFtZXMiOlsiX2RlZmF1bHQiLCJwYWdlIiwic2V0SW50ZXJ2YWwiLCJzZW5kTWVzc2FnZSIsIkpTT04iLCJzdHJpbmdpZnkiLCJldmVudCIsIlJvdXRlciIsInJlYWR5Iiwibm90Rm91bmRTcmNQYWdlIiwic2VsZiIsIl9fTkVYVF9EQVRBX18iLCJwYXRobmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/dev/on-demand-entries-client.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/has-base-path.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/client/has-base-path.js ***!
  \********************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hasBasePath\", ({\n    enumerable: true,\n    get: function() {\n        return hasBasePath;\n    }\n}));\nconst _pathhasprefix = __webpack_require__(/*! ../shared/lib/router/utils/path-has-prefix */ \"./node_modules/next/dist/shared/lib/router/utils/path-has-prefix.js\");\nconst basePath =  false || \"\";\nfunction hasBasePath(path) {\n    return (0, _pathhasprefix.pathHasPrefix)(path, basePath);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=has-base-path.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9oYXMtYmFzZS1wYXRoLmpzIiwibWFwcGluZ3MiOiI7Ozs7K0NBSWdCQTs7O2VBQUFBOzs7MkNBSmM7QUFFOUIsTUFBTUMsV0FBV0MsTUFBbUMsSUFBZTtBQUU1RCxTQUFTRixZQUFZSyxJQUFZO0lBQ3RDLE9BQU9DLENBQUFBLEdBQUFBLGVBQUFBLGFBQWEsRUFBQ0QsTUFBTUo7QUFDN0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvaGFzLWJhc2UtcGF0aC50cz9hMzEyIl0sIm5hbWVzIjpbImhhc0Jhc2VQYXRoIiwiYmFzZVBhdGgiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsInBhdGgiLCJwYXRoSGFzUHJlZml4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/has-base-path.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/dev/noop-turbopack-hmr.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/noop-turbopack-hmr.js ***!
  \*****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// The Turbopack HMR client can't be properly omitted at the moment (WEB-1589),\n// so instead we remap its import to this file in webpack builds.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"connect\", ({\n    enumerable: true,\n    get: function() {\n        return connect;\n    }\n}));\nfunction connect() {}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=noop-turbopack-hmr.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2Rldi9ub29wLXR1cmJvcGFjay1obXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsK0VBQStFO0FBQy9FLGlFQUFpRTs7Ozs7MkNBQ2pEQTs7O2VBQUFBOzs7QUFBVCxTQUFTQSxXQUFXIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2Rldi9ub29wLXR1cmJvcGFjay1obXIudHM/ZTg2NSJdLCJuYW1lcyI6WyJjb25uZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/noop-turbopack-hmr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(app-pages-browser)/./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2dldC1kb21haW4tbG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7bURBT2dCQTs7O2VBQUFBOzs7b0RBSjJCO0FBRTNDLE1BQU1DLFdBQVdDLE1BQW1DLElBQWU7QUFFNUQsU0FBU0YsZ0JBQ2RLLElBQVksRUFDWkMsTUFBdUIsRUFDdkJDLE9BQWtCLEVBQ2xCQyxhQUE4QjtJQUU5QixJQUFJTixLQUErQixFQUFFLEVBZ0JyQyxNQUFPO1FBQ0wsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvZ2V0LWRvbWFpbi1sb2NhbGUudHM/MWQ0ZSJdLCJuYW1lcyI6WyJnZXREb21haW5Mb2NhbGUiLCJiYXNlUGF0aCIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfUk9VVEVSX0JBU0VQQVRIIiwicGF0aCIsImxvY2FsZSIsImxvY2FsZXMiLCJkb21haW5Mb2NhbGVzIiwiX19ORVhUX0kxOE5fU1VQUE9SVCIsIm5vcm1hbGl6ZUxvY2FsZVBhdGgiLCJyZXF1aXJlIiwiZGV0ZWN0RG9tYWluTG9jYWxlIiwidGFyZ2V0IiwiZGV0ZWN0ZWRMb2NhbGUiLCJkb21haW4iLCJ1bmRlZmluZWQiLCJwcm90byIsImh0dHAiLCJmaW5hbExvY2FsZSIsImRlZmF1bHRMb2NhbGUiLCJub3JtYWxpemVQYXRoVHJhaWxpbmdTbGFzaCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/client/has-base-path.js ***!
  \********************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hasBasePath\", ({\n    enumerable: true,\n    get: function() {\n        return hasBasePath;\n    }\n}));\nconst _pathhasprefix = __webpack_require__(/*! ../shared/lib/router/utils/path-has-prefix */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/path-has-prefix.js\");\nconst basePath =  false || \"\";\nfunction hasBasePath(path) {\n    return (0, _pathhasprefix.pathHasPrefix)(path, basePath);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=has-base-path.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2hhcy1iYXNlLXBhdGguanMiLCJtYXBwaW5ncyI6Ijs7OzsrQ0FJZ0JBOzs7ZUFBQUE7OzsyQ0FKYztBQUU5QixNQUFNQyxXQUFXQyxNQUFtQyxJQUFlO0FBRTVELFNBQVNGLFlBQVlLLElBQVk7SUFDdEMsT0FBT0MsQ0FBQUEsR0FBQUEsZUFBQUEsYUFBYSxFQUFDRCxNQUFNSjtBQUM3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9oYXMtYmFzZS1wYXRoLnRzP2EzMTIiXSwibmFtZXMiOlsiaGFzQmFzZVBhdGgiLCJiYXNlUGF0aCIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfUk9VVEVSX0JBU0VQQVRIIiwicGF0aCIsInBhdGhIYXNQcmVmaXgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\n"));

/***/ })

}]);