"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("framework-node_modules_next_dist_client_l",{

/***/ "./node_modules/next/dist/client/next-dev.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/client/next-dev.js ***!
  \***************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// TODO: Remove use of `any` type.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\n__webpack_require__(/*! ./webpack */ \"./node_modules/next/dist/client/webpack.js\");\nconst _ = __webpack_require__(/*! ./ */ \"./node_modules/next/dist/client/index.js\");\nconst _hotmiddlewareclient = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./dev/hot-middleware-client */ \"./node_modules/next/dist/client/dev/hot-middleware-client.js\"));\nconst _pagebootstrap = __webpack_require__(/*! ./page-bootstrap */ \"./node_modules/next/dist/client/page-bootstrap.js\");\n__webpack_require__(/*! ./setup-hydration-warning */ \"./node_modules/next/dist/client/setup-hydration-warning.js\");\nwindow.next = {\n    version: _.version,\n    // router is initialized later so it has to be live-binded\n    get router () {\n        return _.router;\n    },\n    emitter: _.emitter\n};\nconst devClient = (0, _hotmiddlewareclient.default)(\"webpack\");\n(0, _.initialize)({\n    devClient\n}).then((param)=>{\n    let { assetPrefix } = param;\n    return (0, _pagebootstrap.pageBootrap)(assetPrefix);\n}).catch((err)=>{\n    console.error(\"Error was not caught\", err);\n});\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=next-dev.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9uZXh0LWRldi5qcyIsIm1hcHBpbmdzIjoiQUFBQSxrQ0FBa0M7Ozs7OztvQkFDM0I7OEJBQzhDOzBGQUNqQzsyQ0FDUTtvQkFFckI7QUFFUEEsT0FBT0MsSUFBSSxHQUFHO0lBQ1pDLFNBQUFBLEVBQUFBLE9BQU87SUFDUCwwREFBMEQ7SUFDMUQsSUFBSUMsVUFBUztRQUNYLE9BQU9BLEVBQUFBLE1BQU07SUFDZjtJQUNBQyxTQUFBQSxFQUFBQSxPQUFPO0FBQ1Q7QUFFQSxNQUFNQyxZQUFZQyxDQUFBQSxHQUFBQSxxQkFBQUEsT0FBTyxFQUFDO0FBQzFCQyxDQUFBQSxHQUFBQSxFQUFBQSxVQUFVLEVBQUM7SUFBRUY7QUFBVSxHQUNwQkcsSUFBSSxDQUFDLENBQUFDO1FBQUMsRUFBRUMsV0FBVyxFQUFFLEdBQUFEO0lBQ3BCLE9BQU9FLENBQUFBLEdBQUFBLGVBQUFBLFdBQVcsRUFBQ0Q7QUFDckIsR0FDQ0UsS0FBSyxDQUFDLENBQUNDO0lBQ05DLFFBQVFDLEtBQUssQ0FBQyx3QkFBd0JGO0FBQ3hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L25leHQtZGV2LnRzPzExOGYiXSwibmFtZXMiOlsid2luZG93IiwibmV4dCIsInZlcnNpb24iLCJyb3V0ZXIiLCJlbWl0dGVyIiwiZGV2Q2xpZW50IiwiaW5pdEhNUiIsImluaXRpYWxpemUiLCJ0aGVuIiwicGFyYW0iLCJhc3NldFByZWZpeCIsInBhZ2VCb290cmFwIiwiY2F0Y2giLCJlcnIiLCJjb25zb2xlIiwiZXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/next-dev.js\n"));

/***/ })

});