/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_a"],{

/***/ "./node_modules/next/dist/build/deployment-id.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/build/deployment-id.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDeploymentIdQueryOrEmptyString\", ({\n    enumerable: true,\n    get: function() {\n        return getDeploymentIdQueryOrEmptyString;\n    }\n}));\nfunction getDeploymentIdQueryOrEmptyString() {\n    if (false) {}\n    return \"\";\n}\n\n//# sourceMappingURL=deployment-id.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL2RlcGxveW1lbnQtaWQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixxRUFBb0U7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRjtBQUNBLFFBQVEsS0FBOEIsRUFBRSxFQUVuQztBQUNMO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC9kZXBsb3ltZW50LWlkLmpzP2FhZDgiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJnZXREZXBsb3ltZW50SWRRdWVyeU9yRW1wdHlTdHJpbmdcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGdldERlcGxveW1lbnRJZFF1ZXJ5T3JFbXB0eVN0cmluZztcbiAgICB9XG59KTtcbmZ1bmN0aW9uIGdldERlcGxveW1lbnRJZFF1ZXJ5T3JFbXB0eVN0cmluZygpIHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuTkVYVF9ERVBMT1lNRU5UX0lEKSB7XG4gICAgICAgIHJldHVybiBgP2RwbD0ke3Byb2Nlc3MuZW52Lk5FWFRfREVQTE9ZTUVOVF9JRH1gO1xuICAgIH1cbiAgICByZXR1cm4gXCJcIjtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGVwbG95bWVudC1pZC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/deployment-id.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/polyfills/polyfill-module.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/build/polyfills/polyfill-module.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\"trimStart\"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),\"trimEnd\"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),\"description\"in Symbol.prototype||Object.defineProperty(Symbol.prototype,\"description\",{configurable:!0,get:function(){var t=/\\((.*)\\)/.exec(this.toString());return t?t[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(t,r){return r=this.concat.apply([],this),t>1&&r.some(Array.isArray)?r.flat(t-1):r},Array.prototype.flatMap=function(t,r){return this.map(t,r).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(t){if(\"function\"!=typeof t)return this.then(t,t);var r=this.constructor||Promise;return this.then(function(n){return r.resolve(t()).then(function(){return n})},function(n){return r.resolve(t()).then(function(){throw n})})}),Object.fromEntries||(Object.fromEntries=function(t){return Array.from(t).reduce(function(t,r){return t[r[0]]=r[1],t},{})}),Array.prototype.at||(Array.prototype.at=function(t){var r=Math.trunc(t)||0;if(r<0&&(r+=this.length),!(r<0||r>=this.length))return this[r]}),Object.hasOwn||(Object.hasOwn=function(t,r){if(null==t)throw new TypeError(\"Cannot convert undefined or null to object\");return Object.prototype.hasOwnProperty.call(Object(t),r)}),\"canParse\"in URL||(URL.canParse=function(t,r){try{return!!new URL(t,r)}catch(t){return!1}});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3BvbHlmaWxscy9wb2x5ZmlsbC1tb2R1bGUuanMiLCJtYXBwaW5ncyI6IkFBQUEsbVFBQW1RLCtCQUErQix1Q0FBdUMsc0JBQXNCLDREQUE0RCw2RUFBNkUsdUNBQXVDLDRCQUE0QixvRUFBb0UsOENBQThDLGdDQUFnQyw2QkFBNkIsc0NBQXNDLFNBQVMsRUFBRSxhQUFhLHNDQUFzQyxRQUFRLEVBQUUsRUFBRSxzREFBc0QsMENBQTBDLHNCQUFzQixHQUFHLEVBQUUsc0RBQXNELHVCQUF1QiwrREFBK0QsOENBQThDLDZFQUE2RSx5REFBeUQsZ0RBQWdELElBQUkscUJBQXFCLFNBQVMsVUFBVSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3BvbHlmaWxscy9wb2x5ZmlsbC1tb2R1bGUuanM/NjljOCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInRyaW1TdGFydFwiaW4gU3RyaW5nLnByb3RvdHlwZXx8KFN0cmluZy5wcm90b3R5cGUudHJpbVN0YXJ0PVN0cmluZy5wcm90b3R5cGUudHJpbUxlZnQpLFwidHJpbUVuZFwiaW4gU3RyaW5nLnByb3RvdHlwZXx8KFN0cmluZy5wcm90b3R5cGUudHJpbUVuZD1TdHJpbmcucHJvdG90eXBlLnRyaW1SaWdodCksXCJkZXNjcmlwdGlvblwiaW4gU3ltYm9sLnByb3RvdHlwZXx8T2JqZWN0LmRlZmluZVByb3BlcnR5KFN5bWJvbC5wcm90b3R5cGUsXCJkZXNjcmlwdGlvblwiLHtjb25maWd1cmFibGU6ITAsZ2V0OmZ1bmN0aW9uKCl7dmFyIHQ9L1xcKCguKilcXCkvLmV4ZWModGhpcy50b1N0cmluZygpKTtyZXR1cm4gdD90WzFdOnZvaWQgMH19KSxBcnJheS5wcm90b3R5cGUuZmxhdHx8KEFycmF5LnByb3RvdHlwZS5mbGF0PWZ1bmN0aW9uKHQscil7cmV0dXJuIHI9dGhpcy5jb25jYXQuYXBwbHkoW10sdGhpcyksdD4xJiZyLnNvbWUoQXJyYXkuaXNBcnJheSk/ci5mbGF0KHQtMSk6cn0sQXJyYXkucHJvdG90eXBlLmZsYXRNYXA9ZnVuY3Rpb24odCxyKXtyZXR1cm4gdGhpcy5tYXAodCxyKS5mbGF0KCl9KSxQcm9taXNlLnByb3RvdHlwZS5maW5hbGx5fHwoUHJvbWlzZS5wcm90b3R5cGUuZmluYWxseT1mdW5jdGlvbih0KXtpZihcImZ1bmN0aW9uXCIhPXR5cGVvZiB0KXJldHVybiB0aGlzLnRoZW4odCx0KTt2YXIgcj10aGlzLmNvbnN0cnVjdG9yfHxQcm9taXNlO3JldHVybiB0aGlzLnRoZW4oZnVuY3Rpb24obil7cmV0dXJuIHIucmVzb2x2ZSh0KCkpLnRoZW4oZnVuY3Rpb24oKXtyZXR1cm4gbn0pfSxmdW5jdGlvbihuKXtyZXR1cm4gci5yZXNvbHZlKHQoKSkudGhlbihmdW5jdGlvbigpe3Rocm93IG59KX0pfSksT2JqZWN0LmZyb21FbnRyaWVzfHwoT2JqZWN0LmZyb21FbnRyaWVzPWZ1bmN0aW9uKHQpe3JldHVybiBBcnJheS5mcm9tKHQpLnJlZHVjZShmdW5jdGlvbih0LHIpe3JldHVybiB0W3JbMF1dPXJbMV0sdH0se30pfSksQXJyYXkucHJvdG90eXBlLmF0fHwoQXJyYXkucHJvdG90eXBlLmF0PWZ1bmN0aW9uKHQpe3ZhciByPU1hdGgudHJ1bmModCl8fDA7aWYocjwwJiYocis9dGhpcy5sZW5ndGgpLCEocjwwfHxyPj10aGlzLmxlbmd0aCkpcmV0dXJuIHRoaXNbcl19KSxPYmplY3QuaGFzT3dufHwoT2JqZWN0Lmhhc093bj1mdW5jdGlvbih0LHIpe2lmKG51bGw9PXQpdGhyb3cgbmV3IFR5cGVFcnJvcihcIkNhbm5vdCBjb252ZXJ0IHVuZGVmaW5lZCBvciBudWxsIHRvIG9iamVjdFwiKTtyZXR1cm4gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKE9iamVjdCh0KSxyKX0pLFwiY2FuUGFyc2VcImluIFVSTHx8KFVSTC5jYW5QYXJzZT1mdW5jdGlvbih0LHIpe3RyeXtyZXR1cm4hIW5ldyBVUkwodCxyKX1jYXRjaCh0KXtyZXR1cm4hMX19KTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/polyfills/polyfill-module.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/add-base-path.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/client/add-base-path.js ***!
  \********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"addBasePath\", ({\n    enumerable: true,\n    get: function() {\n        return addBasePath;\n    }\n}));\nconst _addpathprefix = __webpack_require__(/*! ../shared/lib/router/utils/add-path-prefix */ \"./node_modules/next/dist/shared/lib/router/utils/add-path-prefix.js\");\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction addBasePath(path, required) {\n    return (0, _normalizetrailingslash.normalizePathTrailingSlash)( false ? 0 : (0, _addpathprefix.addPathPrefix)(path, basePath));\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=add-base-path.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9hZGQtYmFzZS1wYXRoLmpzIiwibWFwcGluZ3MiOiI7Ozs7K0NBS2dCQTs7O2VBQUFBOzs7MkNBTGM7b0RBQ2E7QUFFM0MsTUFBTUMsV0FBV0MsTUFBbUMsSUFBZTtBQUU1RCxTQUFTRixZQUFZSyxJQUFZLEVBQUVDLFFBQWtCO0lBQzFELE9BQU9DLENBQUFBLEdBQUFBLHdCQUFBQSwwQkFBMEIsRUFDL0JMLE1BQStDSSxHQUMzQ0QsQ0FBQUEsR0FDQUksQ0FBQUEsR0FBQUEsZUFBQUEsYUFBYSxFQUFDSixNQUFNSjtBQUU1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9hZGQtYmFzZS1wYXRoLnRzPzUxNzEiXSwibmFtZXMiOlsiYWRkQmFzZVBhdGgiLCJiYXNlUGF0aCIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfUk9VVEVSX0JBU0VQQVRIIiwicGF0aCIsInJlcXVpcmVkIiwibm9ybWFsaXplUGF0aFRyYWlsaW5nU2xhc2giLCJfX05FWFRfTUFOVUFMX0NMSUVOVF9CQVNFX1BBVEgiLCJhZGRQYXRoUHJlZml4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/add-base-path.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/add-locale.js":
/*!*****************************************************!*\
  !*** ./node_modules/next/dist/client/add-locale.js ***!
  \*****************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"addLocale\", ({\n    enumerable: true,\n    get: function() {\n        return addLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst addLocale = function(path) {\n    for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        args[_key - 1] = arguments[_key];\n    }\n    if (false) {}\n    return path;\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=add-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9hZGQtbG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBR2FBOzs7ZUFBQUE7OztvREFGOEI7QUFFcEMsTUFBTUEsWUFBdUIsU0FBQ0MsSUFBQUE7cUNBQVNDLE9BQUFBLElBQUFBLE1BQUFBLE9BQUFBLElBQUFBLE9BQUFBLElBQUFBLElBQUFBLE9BQUFBLEdBQUFBLE9BQUFBLE1BQUFBLE9BQUFBO1FBQUFBLElBQUFBLENBQUFBLE9BQUFBLEVBQUFBLEdBQUFBLFNBQUFBLENBQUFBLEtBQUFBOztJQUM1QyxJQUFJQyxLQUErQixFQUFFLEVBSXJDO0lBQ0EsT0FBT0Y7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9hZGQtbG9jYWxlLnRzP2ZhYWUiXSwibmFtZXMiOlsiYWRkTG9jYWxlIiwicGF0aCIsImFyZ3MiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX0kxOE5fU1VQUE9SVCIsIm5vcm1hbGl6ZVBhdGhUcmFpbGluZ1NsYXNoIiwicmVxdWlyZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/add-locale.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/api/app-dynamic.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/app-dynamic */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=app-dynamic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2FwcC1keW5hbWljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQztBQUNVOztBQUVwRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9hcHAtZHluYW1pYy5qcz9mNTA3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuLi9zaGFyZWQvbGliL2FwcC1keW5hbWljXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIi4uL3NoYXJlZC9saWIvYXBwLWR5bmFtaWNcIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLWR5bmFtaWMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/image.js":
/*!*********************************************!*\
  !*** ./node_modules/next/dist/api/image.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/image-external */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js\");\n/* harmony import */ var _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=image.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2ltYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF1RDtBQUNWOztBQUU3QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9pbWFnZS5qcz82ZGU4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tIFwiLi4vc2hhcmVkL2xpYi9pbWFnZS1leHRlcm5hbFwiO1xuZXhwb3J0ICogZnJvbSBcIi4uL3NoYXJlZC9saWIvaW1hZ2UtZXh0ZXJuYWxcIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW1hZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/image.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/link.js":
/*!********************************************!*\
  !*** ./node_modules/next/dist/api/link.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _client_link__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _client_link__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/link */ \"(app-pages-browser)/./node_modules/next/dist/client/link.js\");\n/* harmony import */ var _client_link__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_link__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_link__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_link__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=link.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2xpbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXlDO0FBQ1Y7O0FBRS9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2xpbmsuanM/MjAwMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIi4uL2NsaWVudC9saW5rXCI7XG5leHBvcnQgKiBmcm9tIFwiLi4vY2xpZW50L2xpbmtcIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bGluay5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzP2MzZDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb25cIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/deployment-id.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/build/deployment-id.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDeploymentIdQueryOrEmptyString\", ({\n    enumerable: true,\n    get: function() {\n        return getDeploymentIdQueryOrEmptyString;\n    }\n}));\nfunction getDeploymentIdQueryOrEmptyString() {\n    if (false) {}\n    return \"\";\n}\n\n//# sourceMappingURL=deployment-id.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvZGVwbG95bWVudC1pZC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLHFFQUFvRTtBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0EsUUFBUSxLQUE4QixFQUFFLEVBRW5DO0FBQ0w7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL2RlcGxveW1lbnQtaWQuanM/MGE0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImdldERlcGxveW1lbnRJZFF1ZXJ5T3JFbXB0eVN0cmluZ1wiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gZ2V0RGVwbG95bWVudElkUXVlcnlPckVtcHR5U3RyaW5nO1xuICAgIH1cbn0pO1xuZnVuY3Rpb24gZ2V0RGVwbG95bWVudElkUXVlcnlPckVtcHR5U3RyaW5nKCkge1xuICAgIGlmIChwcm9jZXNzLmVudi5ORVhUX0RFUExPWU1FTlRfSUQpIHtcbiAgICAgICAgcmV0dXJuIGA/ZHBsPSR7cHJvY2Vzcy5lbnYuTkVYVF9ERVBMT1lNRU5UX0lEfWA7XG4gICAgfVxuICAgIHJldHVybiBcIlwiO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1kZXBsb3ltZW50LWlkLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/deployment-id.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/polyfills/polyfill-module.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/build/polyfills/polyfill-module.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\"trimStart\"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),\"trimEnd\"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),\"description\"in Symbol.prototype||Object.defineProperty(Symbol.prototype,\"description\",{configurable:!0,get:function(){var t=/\\((.*)\\)/.exec(this.toString());return t?t[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(t,r){return r=this.concat.apply([],this),t>1&&r.some(Array.isArray)?r.flat(t-1):r},Array.prototype.flatMap=function(t,r){return this.map(t,r).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(t){if(\"function\"!=typeof t)return this.then(t,t);var r=this.constructor||Promise;return this.then(function(n){return r.resolve(t()).then(function(){return n})},function(n){return r.resolve(t()).then(function(){throw n})})}),Object.fromEntries||(Object.fromEntries=function(t){return Array.from(t).reduce(function(t,r){return t[r[0]]=r[1],t},{})}),Array.prototype.at||(Array.prototype.at=function(t){var r=Math.trunc(t)||0;if(r<0&&(r+=this.length),!(r<0||r>=this.length))return this[r]}),Object.hasOwn||(Object.hasOwn=function(t,r){if(null==t)throw new TypeError(\"Cannot convert undefined or null to object\");return Object.prototype.hasOwnProperty.call(Object(t),r)}),\"canParse\"in URL||(URL.canParse=function(t,r){try{return!!new URL(t,r)}catch(t){return!1}});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/polyfills/polyfill-module.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/build/polyfills/process.js ***!
  \***********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nvar _global_process, _global_process1;\nmodule.exports = ((_global_process = __webpack_require__.g.process) == null ? void 0 : _global_process.env) && typeof ((_global_process1 = __webpack_require__.g.process) == null ? void 0 : _global_process1.env) === \"object\" ? __webpack_require__.g.process : __webpack_require__(/*! next/dist/compiled/process */ \"(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\");\n\n//# sourceMappingURL=process.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLHFDQUFxQyxxQkFBTSxpRkFBaUYscUJBQU0sa0VBQWtFLHFCQUFNLFdBQVcsbUJBQU8sQ0FBQyw0R0FBNEI7O0FBRXpQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanM/YzNlMCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfZ2xvYmFsX3Byb2Nlc3MsIF9nbG9iYWxfcHJvY2VzczE7XG5tb2R1bGUuZXhwb3J0cyA9ICgoX2dsb2JhbF9wcm9jZXNzID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MuZW52KSAmJiB0eXBlb2YgKChfZ2xvYmFsX3Byb2Nlc3MxID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MxLmVudikgPT09IFwib2JqZWN0XCIgPyBnbG9iYWwucHJvY2VzcyA6IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvcHJvY2Vzc1wiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHJvY2Vzcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/client/add-base-path.js ***!
  \********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"addBasePath\", ({\n    enumerable: true,\n    get: function() {\n        return addBasePath;\n    }\n}));\nconst _addpathprefix = __webpack_require__(/*! ../shared/lib/router/utils/add-path-prefix */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/add-path-prefix.js\");\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(app-pages-browser)/./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction addBasePath(path, required) {\n    return (0, _normalizetrailingslash.normalizePathTrailingSlash)( false ? 0 : (0, _addpathprefix.addPathPrefix)(path, basePath));\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=add-base-path.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2FkZC1iYXNlLXBhdGguanMiLCJtYXBwaW5ncyI6Ijs7OzsrQ0FLZ0JBOzs7ZUFBQUE7OzsyQ0FMYztvREFDYTtBQUUzQyxNQUFNQyxXQUFXQyxNQUFtQyxJQUFlO0FBRTVELFNBQVNGLFlBQVlLLElBQVksRUFBRUMsUUFBa0I7SUFDMUQsT0FBT0MsQ0FBQUEsR0FBQUEsd0JBQUFBLDBCQUEwQixFQUMvQkwsTUFBK0NJLEdBQzNDRCxDQUFBQSxHQUNBSSxDQUFBQSxHQUFBQSxlQUFBQSxhQUFhLEVBQUNKLE1BQU1KO0FBRTVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2FkZC1iYXNlLXBhdGgudHM/NTE3MSJdLCJuYW1lcyI6WyJhZGRCYXNlUGF0aCIsImJhc2VQYXRoIiwicHJvY2VzcyIsImVudiIsIl9fTkVYVF9ST1VURVJfQkFTRVBBVEgiLCJwYXRoIiwicmVxdWlyZWQiLCJub3JtYWxpemVQYXRoVHJhaWxpbmdTbGFzaCIsIl9fTkVYVF9NQU5VQUxfQ0xJRU5UX0JBU0VfUEFUSCIsImFkZFBhdGhQcmVmaXgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/add-locale.js":
/*!*****************************************************!*\
  !*** ./node_modules/next/dist/client/add-locale.js ***!
  \*****************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"addLocale\", ({\n    enumerable: true,\n    get: function() {\n        return addLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(app-pages-browser)/./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst addLocale = function(path) {\n    for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        args[_key - 1] = arguments[_key];\n    }\n    if (false) {}\n    return path;\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=add-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2FkZC1sb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs2Q0FHYUE7OztlQUFBQTs7O29EQUY4QjtBQUVwQyxNQUFNQSxZQUF1QixTQUFDQyxJQUFBQTtxQ0FBU0MsT0FBQUEsSUFBQUEsTUFBQUEsT0FBQUEsSUFBQUEsT0FBQUEsSUFBQUEsSUFBQUEsT0FBQUEsR0FBQUEsT0FBQUEsTUFBQUEsT0FBQUE7UUFBQUEsSUFBQUEsQ0FBQUEsT0FBQUEsRUFBQUEsR0FBQUEsU0FBQUEsQ0FBQUEsS0FBQUE7O0lBQzVDLElBQUlDLEtBQStCLEVBQUUsRUFJckM7SUFDQSxPQUFPRjtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2FkZC1sb2NhbGUudHM/ZmFhZSJdLCJuYW1lcyI6WyJhZGRMb2NhbGUiLCJwYXRoIiwiYXJncyIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfSTE4Tl9TVVBQT1JUIiwibm9ybWFsaXplUGF0aFRyYWlsaW5nU2xhc2giLCJyZXF1aXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/add-locale.js\n"));

/***/ })

}]);