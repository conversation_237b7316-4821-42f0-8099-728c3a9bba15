"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_B"],{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/container/BuildError.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/container/BuildError.js ***!
  \*****************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    BuildError: function() {\n        return BuildError;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _Dialog = __webpack_require__(/*! ../components/Dialog */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js\");\nconst _Overlay = __webpack_require__(/*! ../components/Overlay */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/index.js\");\nconst _Terminal = __webpack_require__(/*! ../components/Terminal */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Terminal/index.js\");\nconst _VersionStalenessInfo = __webpack_require__(/*! ../components/VersionStalenessInfo */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/index.js\");\nconst _nooptemplate = __webpack_require__(/*! ../helpers/noop-template */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  .nextjs-container-errors-header > h1 {\\n    font-size: var(--size-font-big);\\n    line-height: var(--size-font-bigger);\\n    font-weight: bold;\\n    margin: var(--size-gap-double) 0;\\n  }\\n  .nextjs-container-errors-header p {\\n    font-size: var(--size-font-small);\\n    line-height: var(--size-font-big);\\n    white-space: pre-wrap;\\n  }\\n  .nextjs-container-errors-body footer {\\n    margin-top: var(--size-gap);\\n  }\\n  .nextjs-container-errors-body footer p {\\n    margin: 0;\\n  }\\n\\n  .nextjs-container-errors-body small {\\n    color: var(--color-font);\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst BuildError = function BuildError(param) {\n    _s();\n    let { message, versionInfo } = param;\n    const noop = _react.useCallback(()=>{}, []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_Overlay.Overlay, {\n        fixed: true,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_Dialog.Dialog, {\n            type: \"error\",\n            \"aria-labelledby\": \"nextjs__container_error_label\",\n            \"aria-describedby\": \"nextjs__container_error_desc\",\n            onClose: noop,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Dialog.DialogContent, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Dialog.DialogHeader, {\n                        className: \"nextjs-container-errors-header\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                                id: \"nextjs__container_errors_label\",\n                                children: \"Build Error\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                id: \"nextjs__container_errors_desc\",\n                                className: \"nextjs__container_errors_desc\",\n                                children: \"Failed to compile\"\n                            }),\n                            versionInfo ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_VersionStalenessInfo.VersionStalenessInfo, {\n                                ...versionInfo\n                            }) : null\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Dialog.DialogBody, {\n                        className: \"nextjs-container-errors-body\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Terminal.Terminal, {\n                                content: message\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"footer\", {\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    id: \"nextjs__container_build_error_desc\",\n                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"small\", {\n                                        children: \"This error occurred during the build process and can only be dismissed by fixing the error.\"\n                                    })\n                                })\n                            })\n                        ]\n                    })\n                ]\n            })\n        })\n    });\n};\n_s(BuildError, \"hHp3im7kVRcdOeKmK0+3cSEjzLI=\");\n_c = BuildError;\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=BuildError.js.map\nvar _c;\n$RefreshReg$(_c, \"BuildError\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/container/BuildError.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/container/Errors.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/container/Errors.js ***!
  \*************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Errors: function() {\n        return Errors;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nconst _shared = __webpack_require__(/*! ../../shared */ \"./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _Dialog = __webpack_require__(/*! ../components/Dialog */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js\");\nconst _LeftRightDialogHeader = __webpack_require__(/*! ../components/LeftRightDialogHeader */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/index.js\");\nconst _Overlay = __webpack_require__(/*! ../components/Overlay */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/index.js\");\nconst _Toast = __webpack_require__(/*! ../components/Toast */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Toast/index.js\");\nconst _getErrorByType = __webpack_require__(/*! ../helpers/getErrorByType */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js\");\nconst _nooptemplate = __webpack_require__(/*! ../helpers/noop-template */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nconst _CloseIcon = __webpack_require__(/*! ../icons/CloseIcon */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon.js\");\nconst _RuntimeError = __webpack_require__(/*! ./RuntimeError */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/index.js\");\nconst _VersionStalenessInfo = __webpack_require__(/*! ../components/VersionStalenessInfo */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/index.js\");\nconst _errorsource = __webpack_require__(/*! ../../../../../shared/lib/error-source */ \"./node_modules/next/dist/shared/lib/error-source.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../components/hot-linked-text */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/hot-linked-text/index.js\");\nconst _componentstackpseudohtml = __webpack_require__(/*! ./RuntimeError/component-stack-pseudo-html */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/component-stack-pseudo-html.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ../helpers/hydration-error-info */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/hydration-error-info.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  .nextjs-container-errors-header > h1 {\\n    font-size: var(--size-font-big);\\n    line-height: var(--size-font-bigger);\\n    font-weight: bold;\\n    margin: calc(var(--size-gap-double) * 1.5) 0;\\n    color: var(--color-title-h1);\\n  }\\n  .nextjs-container-errors-header small {\\n    font-size: var(--size-font-small);\\n    color: var(--color-accents-1);\\n    margin-left: var(--size-gap-double);\\n  }\\n  .nextjs-container-errors-header small > span {\\n    font-family: var(--font-stack-monospace);\\n  }\\n  .nextjs-container-errors-header p {\\n    font-size: var(--size-font-small);\\n    line-height: var(--size-font-big);\\n    white-space: pre-wrap;\\n  }\\n  .nextjs__container_errors_desc {\\n    font-family: var(--font-stack-monospace);\\n    padding: var(--size-gap) var(--size-gap-double);\\n    border-left: 2px solid var(--color-text-color-red-1);\\n    margin-top: var(--size-gap);\\n    font-weight: bold;\\n    color: var(--color-text-color-red-1);\\n    background-color: var(--color-text-background-red-1);\\n  }\\n  p.nextjs__container_errors__notes {\\n    margin: var(--size-gap-double) auto;\\n    color: var(--color-stack-notes);\\n    font-weight: 600;\\n    font-size: 15px;\\n  }\\n  .nextjs-container-errors-header > div > small {\\n    margin: 0;\\n    margin-top: var(--size-gap-half);\\n  }\\n  .nextjs-container-errors-header > p > a {\\n    color: inherit;\\n    font-weight: bold;\\n  }\\n  .nextjs-container-errors-body > h2:not(:first-child) {\\n    margin-top: calc(var(--size-gap-double) + var(--size-gap));\\n  }\\n  .nextjs-container-errors-body > h2 {\\n    color: var(--color-title-color);\\n    margin-bottom: var(--size-gap);\\n    font-size: var(--size-font-big);\\n  }\\n  .nextjs__container_errors__component-stack {\\n    padding: 12px 32px;\\n    color: var(--color-ansi-fg);\\n    background: var(--color-ansi-bg);\\n  }\\n  .nextjs-toast-errors-parent {\\n    cursor: pointer;\\n    transition: transform 0.2s ease;\\n  }\\n  .nextjs-toast-errors-parent:hover {\\n    transform: scale(1.1);\\n  }\\n  .nextjs-toast-errors {\\n    display: flex;\\n    align-items: center;\\n    justify-content: flex-start;\\n  }\\n  .nextjs-toast-errors > svg {\\n    margin-right: var(--size-gap);\\n  }\\n  .nextjs-toast-errors-hide-button {\\n    margin-left: var(--size-gap-triple);\\n    border: none;\\n    background: none;\\n    color: var(--color-ansi-bright-white);\\n    padding: 0;\\n    transition: opacity 0.25s ease;\\n    opacity: 0.7;\\n  }\\n  .nextjs-toast-errors-hide-button:hover {\\n    opacity: 1;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction isNextjsLink(text) {\n    return text.startsWith(\"https://nextjs.org\");\n}\nfunction getErrorSignature(ev) {\n    const { event } = ev;\n    switch(event.type){\n        case _shared.ACTION_UNHANDLED_ERROR:\n        case _shared.ACTION_UNHANDLED_REJECTION:\n            {\n                return event.reason.name + \"::\" + event.reason.message + \"::\" + event.reason.stack;\n            }\n        default:\n            {}\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const _ = event;\n    return \"\";\n}\nfunction Errors(param) {\n    let { isAppDir, errors, initialDisplayState, versionInfo } = param;\n    var _activeError_componentStackFrames;\n    const [lookups, setLookups] = (0, _react.useState)({});\n    const [readyErrors, nextError] = (0, _react.useMemo)(()=>{\n        let ready = [];\n        let next = null;\n        // Ensure errors are displayed in the order they occurred in:\n        for(let idx = 0; idx < errors.length; ++idx){\n            const e = errors[idx];\n            const { id } = e;\n            if (id in lookups) {\n                ready.push(lookups[id]);\n                continue;\n            }\n            // Check for duplicate errors\n            if (idx > 0) {\n                const prev = errors[idx - 1];\n                if (getErrorSignature(prev) === getErrorSignature(e)) {\n                    continue;\n                }\n            }\n            next = e;\n            break;\n        }\n        return [\n            ready,\n            next\n        ];\n    }, [\n        errors,\n        lookups\n    ]);\n    const isLoading = (0, _react.useMemo)(()=>{\n        return readyErrors.length < 1 && Boolean(errors.length);\n    }, [\n        errors.length,\n        readyErrors.length\n    ]);\n    (0, _react.useEffect)(()=>{\n        if (nextError == null) {\n            return;\n        }\n        let mounted = true;\n        (0, _getErrorByType.getErrorByType)(nextError, isAppDir).then((resolved)=>{\n            // We don't care if the desired error changed while we were resolving,\n            // thus we're not tracking it using a ref. Once the work has been done,\n            // we'll store it.\n            if (mounted) {\n                setLookups((m)=>({\n                        ...m,\n                        [resolved.id]: resolved\n                    }));\n            }\n        }, ()=>{\n        // TODO: handle this, though an edge case\n        });\n        return ()=>{\n            mounted = false;\n        };\n    }, [\n        nextError,\n        isAppDir\n    ]);\n    const [displayState, setDisplayState] = (0, _react.useState)(initialDisplayState);\n    const [activeIdx, setActiveIndex] = (0, _react.useState)(0);\n    const previous = (0, _react.useCallback)(()=>setActiveIndex((v)=>Math.max(0, v - 1)), []);\n    const next = (0, _react.useCallback)(()=>setActiveIndex((v)=>Math.max(0, Math.min(readyErrors.length - 1, v + 1))), [\n        readyErrors.length\n    ]);\n    const activeError = (0, _react.useMemo)(()=>{\n        var _readyErrors_activeIdx;\n        return (_readyErrors_activeIdx = readyErrors[activeIdx]) != null ? _readyErrors_activeIdx : null;\n    }, [\n        activeIdx,\n        readyErrors\n    ]);\n    // Reset component state when there are no errors to be displayed.\n    // This should never happen, but lets handle it.\n    (0, _react.useEffect)(()=>{\n        if (errors.length < 1) {\n            setLookups({});\n            setDisplayState(\"hidden\");\n            setActiveIndex(0);\n        }\n    }, [\n        errors.length\n    ]);\n    const minimize = (0, _react.useCallback)(()=>setDisplayState(\"minimized\"), []);\n    const hide = (0, _react.useCallback)(()=>setDisplayState(\"hidden\"), []);\n    const fullscreen = (0, _react.useCallback)(()=>setDisplayState(\"fullscreen\"), []);\n    // This component shouldn't be rendered with no errors, but if it is, let's\n    // handle it gracefully by rendering nothing.\n    if (errors.length < 1 || activeError == null) {\n        return null;\n    }\n    if (isLoading) {\n        // TODO: better loading state\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_Overlay.Overlay, {});\n    }\n    if (displayState === \"hidden\") {\n        return null;\n    }\n    if (displayState === \"minimized\") {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_Toast.Toast, {\n            className: \"nextjs-toast-errors-parent\",\n            onClick: fullscreen,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                className: \"nextjs-toast-errors\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        width: \"24\",\n                        height: \"24\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"circle\", {\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"line\", {\n                                x1: \"12\",\n                                y1: \"8\",\n                                x2: \"12\",\n                                y2: \"12\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"line\", {\n                                x1: \"12\",\n                                y1: \"16\",\n                                x2: \"12.01\",\n                                y2: \"16\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                        children: [\n                            readyErrors.length,\n                            \" error\",\n                            readyErrors.length > 1 ? \"s\" : \"\"\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                        \"data-nextjs-toast-errors-hide-button\": true,\n                        className: \"nextjs-toast-errors-hide-button\",\n                        type: \"button\",\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            hide();\n                        },\n                        \"aria-label\": \"Hide Errors\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_CloseIcon.CloseIcon, {})\n                    })\n                ]\n            })\n        });\n    }\n    const error = activeError.error;\n    const isServerError = [\n        \"server\",\n        \"edge-server\"\n    ].includes((0, _errorsource.getErrorSource)(error) || \"\");\n    const errorDetails = error.details || {};\n    const [warningTemplate, serverContent, clientContent] = errorDetails.warning || [\n        null,\n        \"\",\n        \"\"\n    ];\n    const hydrationErrorType = (0, _hydrationerrorinfo.getHydrationWarningType)(warningTemplate);\n    const hydrationWarning = warningTemplate ? warningTemplate.replace(\"%s\", serverContent).replace(\"%s\", clientContent).replace(\"%s\", \"\") // remove the %s for stack\n    .replace(/%s$/, \"\") // If there's still a %s at the end, remove it\n    .replace(/^Warning: /, \"\") : null;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_Overlay.Overlay, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_Dialog.Dialog, {\n            type: \"error\",\n            \"aria-labelledby\": \"nextjs__container_errors_label\",\n            \"aria-describedby\": \"nextjs__container_errors_desc\",\n            onClose: isServerError ? undefined : minimize,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Dialog.DialogContent, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Dialog.DialogHeader, {\n                        className: \"nextjs-container-errors-header\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_LeftRightDialogHeader.LeftRightDialogHeader, {\n                                previous: activeIdx > 0 ? previous : null,\n                                next: activeIdx < readyErrors.length - 1 ? next : null,\n                                close: isServerError ? undefined : minimize,\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"small\", {\n                                        children: [\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                children: activeIdx + 1\n                                            }),\n                                            \" of\",\n                                            \" \",\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                \"data-nextjs-dialog-header-total-count\": true,\n                                                children: readyErrors.length\n                                            }),\n                                            \" error\",\n                                            readyErrors.length < 2 ? \"\" : \"s\"\n                                        ]\n                                    }),\n                                    versionInfo ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_VersionStalenessInfo.VersionStalenessInfo, {\n                                        ...versionInfo\n                                    }) : null\n                                ]\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                                id: \"nextjs__container_errors_label\",\n                                children: isServerError ? \"Server Error\" : \"Unhandled Runtime Error\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                                id: \"nextjs__container_errors_desc\",\n                                className: \"nextjs__container_errors_desc\",\n                                children: [\n                                    error.name,\n                                    \":\",\n                                    \" \",\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                                        text: error.message,\n                                        matcher: isNextjsLink\n                                    })\n                                ]\n                            }),\n                            hydrationWarning && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                        id: \"nextjs__container_errors__notes\",\n                                        className: \"nextjs__container_errors__notes\",\n                                        children: hydrationWarning\n                                    }),\n                                    ((_activeError_componentStackFrames = activeError.componentStackFrames) == null ? void 0 : _activeError_componentStackFrames.length) ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_componentstackpseudohtml.PseudoHtmlDiff, {\n                                        className: \"nextjs__container_errors__component-stack\",\n                                        hydrationMismatchType: hydrationErrorType,\n                                        componentStackFrames: activeError.componentStackFrames,\n                                        firstContent: serverContent,\n                                        secondContent: clientContent\n                                    }) : null\n                                ]\n                            }),\n                            isServerError ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"small\", {\n                                    children: \"This error happened while generating the page. Any console logs will be displayed in the terminal window.\"\n                                })\n                            }) : undefined\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Dialog.DialogBody, {\n                        className: \"nextjs-container-errors-body\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_RuntimeError.RuntimeError, {\n                            error: activeError\n                        }, activeError.id.toString())\n                    })\n                ]\n            })\n        })\n    });\n}\n_c = Errors;\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=Errors.js.map\nvar _c;\n$RefreshReg$(_c, \"Errors\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/container/Errors.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/BuildError.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/container/BuildError.js ***!
  \*****************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    BuildError: function() {\n        return BuildError;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _Dialog = __webpack_require__(/*! ../components/Dialog */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js\");\nconst _Overlay = __webpack_require__(/*! ../components/Overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/index.js\");\nconst _Terminal = __webpack_require__(/*! ../components/Terminal */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Terminal/index.js\");\nconst _VersionStalenessInfo = __webpack_require__(/*! ../components/VersionStalenessInfo */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/index.js\");\nconst _nooptemplate = __webpack_require__(/*! ../helpers/noop-template */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  .nextjs-container-errors-header > h1 {\\n    font-size: var(--size-font-big);\\n    line-height: var(--size-font-bigger);\\n    font-weight: bold;\\n    margin: var(--size-gap-double) 0;\\n  }\\n  .nextjs-container-errors-header p {\\n    font-size: var(--size-font-small);\\n    line-height: var(--size-font-big);\\n    white-space: pre-wrap;\\n  }\\n  .nextjs-container-errors-body footer {\\n    margin-top: var(--size-gap);\\n  }\\n  .nextjs-container-errors-body footer p {\\n    margin: 0;\\n  }\\n\\n  .nextjs-container-errors-body small {\\n    color: var(--color-font);\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst BuildError = function BuildError(param) {\n    _s();\n    let { message, versionInfo } = param;\n    const noop = _react.useCallback(()=>{}, []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_Overlay.Overlay, {\n        fixed: true,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_Dialog.Dialog, {\n            type: \"error\",\n            \"aria-labelledby\": \"nextjs__container_error_label\",\n            \"aria-describedby\": \"nextjs__container_error_desc\",\n            onClose: noop,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Dialog.DialogContent, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Dialog.DialogHeader, {\n                        className: \"nextjs-container-errors-header\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                                id: \"nextjs__container_errors_label\",\n                                children: \"Build Error\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                id: \"nextjs__container_errors_desc\",\n                                className: \"nextjs__container_errors_desc\",\n                                children: \"Failed to compile\"\n                            }),\n                            versionInfo ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_VersionStalenessInfo.VersionStalenessInfo, {\n                                ...versionInfo\n                            }) : null\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Dialog.DialogBody, {\n                        className: \"nextjs-container-errors-body\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Terminal.Terminal, {\n                                content: message\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"footer\", {\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    id: \"nextjs__container_build_error_desc\",\n                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"small\", {\n                                        children: \"This error occurred during the build process and can only be dismissed by fixing the error.\"\n                                    })\n                                })\n                            })\n                        ]\n                    })\n                ]\n            })\n        })\n    });\n};\n_s(BuildError, \"hHp3im7kVRcdOeKmK0+3cSEjzLI=\");\n_c = BuildError;\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=BuildError.js.map\nvar _c;\n$RefreshReg$(_c, \"BuildError\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/BuildError.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/Errors.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/container/Errors.js ***!
  \*************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Errors: function() {\n        return Errors;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _shared = __webpack_require__(/*! ../../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _Dialog = __webpack_require__(/*! ../components/Dialog */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js\");\nconst _LeftRightDialogHeader = __webpack_require__(/*! ../components/LeftRightDialogHeader */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/index.js\");\nconst _Overlay = __webpack_require__(/*! ../components/Overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/index.js\");\nconst _Toast = __webpack_require__(/*! ../components/Toast */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Toast/index.js\");\nconst _getErrorByType = __webpack_require__(/*! ../helpers/getErrorByType */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js\");\nconst _nooptemplate = __webpack_require__(/*! ../helpers/noop-template */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nconst _CloseIcon = __webpack_require__(/*! ../icons/CloseIcon */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon.js\");\nconst _RuntimeError = __webpack_require__(/*! ./RuntimeError */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/index.js\");\nconst _VersionStalenessInfo = __webpack_require__(/*! ../components/VersionStalenessInfo */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/index.js\");\nconst _errorsource = __webpack_require__(/*! ../../../../../shared/lib/error-source */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/error-source.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../components/hot-linked-text */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/hot-linked-text/index.js\");\nconst _componentstackpseudohtml = __webpack_require__(/*! ./RuntimeError/component-stack-pseudo-html */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/component-stack-pseudo-html.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ../helpers/hydration-error-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/hydration-error-info.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  .nextjs-container-errors-header > h1 {\\n    font-size: var(--size-font-big);\\n    line-height: var(--size-font-bigger);\\n    font-weight: bold;\\n    margin: calc(var(--size-gap-double) * 1.5) 0;\\n    color: var(--color-title-h1);\\n  }\\n  .nextjs-container-errors-header small {\\n    font-size: var(--size-font-small);\\n    color: var(--color-accents-1);\\n    margin-left: var(--size-gap-double);\\n  }\\n  .nextjs-container-errors-header small > span {\\n    font-family: var(--font-stack-monospace);\\n  }\\n  .nextjs-container-errors-header p {\\n    font-size: var(--size-font-small);\\n    line-height: var(--size-font-big);\\n    white-space: pre-wrap;\\n  }\\n  .nextjs__container_errors_desc {\\n    font-family: var(--font-stack-monospace);\\n    padding: var(--size-gap) var(--size-gap-double);\\n    border-left: 2px solid var(--color-text-color-red-1);\\n    margin-top: var(--size-gap);\\n    font-weight: bold;\\n    color: var(--color-text-color-red-1);\\n    background-color: var(--color-text-background-red-1);\\n  }\\n  p.nextjs__container_errors__notes {\\n    margin: var(--size-gap-double) auto;\\n    color: var(--color-stack-notes);\\n    font-weight: 600;\\n    font-size: 15px;\\n  }\\n  .nextjs-container-errors-header > div > small {\\n    margin: 0;\\n    margin-top: var(--size-gap-half);\\n  }\\n  .nextjs-container-errors-header > p > a {\\n    color: inherit;\\n    font-weight: bold;\\n  }\\n  .nextjs-container-errors-body > h2:not(:first-child) {\\n    margin-top: calc(var(--size-gap-double) + var(--size-gap));\\n  }\\n  .nextjs-container-errors-body > h2 {\\n    color: var(--color-title-color);\\n    margin-bottom: var(--size-gap);\\n    font-size: var(--size-font-big);\\n  }\\n  .nextjs__container_errors__component-stack {\\n    padding: 12px 32px;\\n    color: var(--color-ansi-fg);\\n    background: var(--color-ansi-bg);\\n  }\\n  .nextjs-toast-errors-parent {\\n    cursor: pointer;\\n    transition: transform 0.2s ease;\\n  }\\n  .nextjs-toast-errors-parent:hover {\\n    transform: scale(1.1);\\n  }\\n  .nextjs-toast-errors {\\n    display: flex;\\n    align-items: center;\\n    justify-content: flex-start;\\n  }\\n  .nextjs-toast-errors > svg {\\n    margin-right: var(--size-gap);\\n  }\\n  .nextjs-toast-errors-hide-button {\\n    margin-left: var(--size-gap-triple);\\n    border: none;\\n    background: none;\\n    color: var(--color-ansi-bright-white);\\n    padding: 0;\\n    transition: opacity 0.25s ease;\\n    opacity: 0.7;\\n  }\\n  .nextjs-toast-errors-hide-button:hover {\\n    opacity: 1;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction isNextjsLink(text) {\n    return text.startsWith(\"https://nextjs.org\");\n}\nfunction getErrorSignature(ev) {\n    const { event } = ev;\n    switch(event.type){\n        case _shared.ACTION_UNHANDLED_ERROR:\n        case _shared.ACTION_UNHANDLED_REJECTION:\n            {\n                return event.reason.name + \"::\" + event.reason.message + \"::\" + event.reason.stack;\n            }\n        default:\n            {}\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const _ = event;\n    return \"\";\n}\nfunction Errors(param) {\n    let { isAppDir, errors, initialDisplayState, versionInfo } = param;\n    var _activeError_componentStackFrames;\n    const [lookups, setLookups] = (0, _react.useState)({});\n    const [readyErrors, nextError] = (0, _react.useMemo)(()=>{\n        let ready = [];\n        let next = null;\n        // Ensure errors are displayed in the order they occurred in:\n        for(let idx = 0; idx < errors.length; ++idx){\n            const e = errors[idx];\n            const { id } = e;\n            if (id in lookups) {\n                ready.push(lookups[id]);\n                continue;\n            }\n            // Check for duplicate errors\n            if (idx > 0) {\n                const prev = errors[idx - 1];\n                if (getErrorSignature(prev) === getErrorSignature(e)) {\n                    continue;\n                }\n            }\n            next = e;\n            break;\n        }\n        return [\n            ready,\n            next\n        ];\n    }, [\n        errors,\n        lookups\n    ]);\n    const isLoading = (0, _react.useMemo)(()=>{\n        return readyErrors.length < 1 && Boolean(errors.length);\n    }, [\n        errors.length,\n        readyErrors.length\n    ]);\n    (0, _react.useEffect)(()=>{\n        if (nextError == null) {\n            return;\n        }\n        let mounted = true;\n        (0, _getErrorByType.getErrorByType)(nextError, isAppDir).then((resolved)=>{\n            // We don't care if the desired error changed while we were resolving,\n            // thus we're not tracking it using a ref. Once the work has been done,\n            // we'll store it.\n            if (mounted) {\n                setLookups((m)=>({\n                        ...m,\n                        [resolved.id]: resolved\n                    }));\n            }\n        }, ()=>{\n        // TODO: handle this, though an edge case\n        });\n        return ()=>{\n            mounted = false;\n        };\n    }, [\n        nextError,\n        isAppDir\n    ]);\n    const [displayState, setDisplayState] = (0, _react.useState)(initialDisplayState);\n    const [activeIdx, setActiveIndex] = (0, _react.useState)(0);\n    const previous = (0, _react.useCallback)(()=>setActiveIndex((v)=>Math.max(0, v - 1)), []);\n    const next = (0, _react.useCallback)(()=>setActiveIndex((v)=>Math.max(0, Math.min(readyErrors.length - 1, v + 1))), [\n        readyErrors.length\n    ]);\n    const activeError = (0, _react.useMemo)(()=>{\n        var _readyErrors_activeIdx;\n        return (_readyErrors_activeIdx = readyErrors[activeIdx]) != null ? _readyErrors_activeIdx : null;\n    }, [\n        activeIdx,\n        readyErrors\n    ]);\n    // Reset component state when there are no errors to be displayed.\n    // This should never happen, but lets handle it.\n    (0, _react.useEffect)(()=>{\n        if (errors.length < 1) {\n            setLookups({});\n            setDisplayState(\"hidden\");\n            setActiveIndex(0);\n        }\n    }, [\n        errors.length\n    ]);\n    const minimize = (0, _react.useCallback)(()=>setDisplayState(\"minimized\"), []);\n    const hide = (0, _react.useCallback)(()=>setDisplayState(\"hidden\"), []);\n    const fullscreen = (0, _react.useCallback)(()=>setDisplayState(\"fullscreen\"), []);\n    // This component shouldn't be rendered with no errors, but if it is, let's\n    // handle it gracefully by rendering nothing.\n    if (errors.length < 1 || activeError == null) {\n        return null;\n    }\n    if (isLoading) {\n        // TODO: better loading state\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_Overlay.Overlay, {});\n    }\n    if (displayState === \"hidden\") {\n        return null;\n    }\n    if (displayState === \"minimized\") {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_Toast.Toast, {\n            className: \"nextjs-toast-errors-parent\",\n            onClick: fullscreen,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                className: \"nextjs-toast-errors\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        width: \"24\",\n                        height: \"24\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"circle\", {\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"line\", {\n                                x1: \"12\",\n                                y1: \"8\",\n                                x2: \"12\",\n                                y2: \"12\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"line\", {\n                                x1: \"12\",\n                                y1: \"16\",\n                                x2: \"12.01\",\n                                y2: \"16\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                        children: [\n                            readyErrors.length,\n                            \" error\",\n                            readyErrors.length > 1 ? \"s\" : \"\"\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                        \"data-nextjs-toast-errors-hide-button\": true,\n                        className: \"nextjs-toast-errors-hide-button\",\n                        type: \"button\",\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            hide();\n                        },\n                        \"aria-label\": \"Hide Errors\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_CloseIcon.CloseIcon, {})\n                    })\n                ]\n            })\n        });\n    }\n    const error = activeError.error;\n    const isServerError = [\n        \"server\",\n        \"edge-server\"\n    ].includes((0, _errorsource.getErrorSource)(error) || \"\");\n    const errorDetails = error.details || {};\n    const [warningTemplate, serverContent, clientContent] = errorDetails.warning || [\n        null,\n        \"\",\n        \"\"\n    ];\n    const hydrationErrorType = (0, _hydrationerrorinfo.getHydrationWarningType)(warningTemplate);\n    const hydrationWarning = warningTemplate ? warningTemplate.replace(\"%s\", serverContent).replace(\"%s\", clientContent).replace(\"%s\", \"\") // remove the %s for stack\n    .replace(/%s$/, \"\") // If there's still a %s at the end, remove it\n    .replace(/^Warning: /, \"\") : null;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_Overlay.Overlay, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_Dialog.Dialog, {\n            type: \"error\",\n            \"aria-labelledby\": \"nextjs__container_errors_label\",\n            \"aria-describedby\": \"nextjs__container_errors_desc\",\n            onClose: isServerError ? undefined : minimize,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Dialog.DialogContent, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Dialog.DialogHeader, {\n                        className: \"nextjs-container-errors-header\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_LeftRightDialogHeader.LeftRightDialogHeader, {\n                                previous: activeIdx > 0 ? previous : null,\n                                next: activeIdx < readyErrors.length - 1 ? next : null,\n                                close: isServerError ? undefined : minimize,\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"small\", {\n                                        children: [\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                children: activeIdx + 1\n                                            }),\n                                            \" of\",\n                                            \" \",\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                \"data-nextjs-dialog-header-total-count\": true,\n                                                children: readyErrors.length\n                                            }),\n                                            \" error\",\n                                            readyErrors.length < 2 ? \"\" : \"s\"\n                                        ]\n                                    }),\n                                    versionInfo ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_VersionStalenessInfo.VersionStalenessInfo, {\n                                        ...versionInfo\n                                    }) : null\n                                ]\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                                id: \"nextjs__container_errors_label\",\n                                children: isServerError ? \"Server Error\" : \"Unhandled Runtime Error\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                                id: \"nextjs__container_errors_desc\",\n                                className: \"nextjs__container_errors_desc\",\n                                children: [\n                                    error.name,\n                                    \":\",\n                                    \" \",\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                                        text: error.message,\n                                        matcher: isNextjsLink\n                                    })\n                                ]\n                            }),\n                            hydrationWarning && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                        id: \"nextjs__container_errors__notes\",\n                                        className: \"nextjs__container_errors__notes\",\n                                        children: hydrationWarning\n                                    }),\n                                    ((_activeError_componentStackFrames = activeError.componentStackFrames) == null ? void 0 : _activeError_componentStackFrames.length) ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_componentstackpseudohtml.PseudoHtmlDiff, {\n                                        className: \"nextjs__container_errors__component-stack\",\n                                        hydrationMismatchType: hydrationErrorType,\n                                        componentStackFrames: activeError.componentStackFrames,\n                                        firstContent: serverContent,\n                                        secondContent: clientContent\n                                    }) : null\n                                ]\n                            }),\n                            isServerError ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"small\", {\n                                    children: \"This error happened while generating the page. Any console logs will be displayed in the terminal window.\"\n                                })\n                            }) : undefined\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Dialog.DialogBody, {\n                        className: \"nextjs-container-errors-body\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_RuntimeError.RuntimeError, {\n                            error: activeError\n                        }, activeError.id.toString())\n                    })\n                ]\n            })\n        })\n    });\n}\n_c = Errors;\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=Errors.js.map\nvar _c;\n$RefreshReg$(_c, \"Errors\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/Errors.js\n"));

/***/ })

}]);