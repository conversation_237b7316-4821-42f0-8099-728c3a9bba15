"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_ge"],{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/get-socket-url.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/get-socket-url.js ***!
  \*******************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getSocketUrl\", ({\n    enumerable: true,\n    get: function() {\n        return getSocketUrl;\n    }\n}));\nconst _normalizedassetprefix = __webpack_require__(/*! ../../../../../shared/lib/normalized-asset-prefix */ \"./node_modules/next/dist/shared/lib/normalized-asset-prefix.js\");\nfunction getSocketProtocol(assetPrefix) {\n    let protocol = window.location.protocol;\n    try {\n        // assetPrefix is a url\n        protocol = new URL(assetPrefix).protocol;\n    } catch (e) {}\n    return protocol === \"http:\" ? \"ws:\" : \"wss:\";\n}\nfunction getSocketUrl(assetPrefix) {\n    const prefix = (0, _normalizedassetprefix.normalizedAssetPrefix)(assetPrefix);\n    const protocol = getSocketProtocol(assetPrefix || \"\");\n    if (URL.canParse(prefix)) {\n        // since normalized asset prefix is ensured to be a URL format,\n        // we can safely replace the protocol\n        return prefix.replace(/^http/, \"ws\");\n    }\n    const { hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? \":\" + port : \"\") + prefix;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-socket-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvZ2V0LXNvY2tldC11cmwuanMiLCJtYXBwaW5ncyI6Ijs7OztnREFhZ0JBOzs7ZUFBQUE7OzttREFic0I7QUFFdEMsU0FBU0Msa0JBQWtCQyxXQUFtQjtJQUM1QyxJQUFJQyxXQUFXQyxPQUFPQyxRQUFRLENBQUNGLFFBQVE7SUFFdkMsSUFBSTtRQUNGLHVCQUF1QjtRQUN2QkEsV0FBVyxJQUFJRyxJQUFJSixhQUFhQyxRQUFRO0lBQzFDLEVBQUUsT0FBQUksR0FBTSxDQUFDO0lBRVQsT0FBT0osYUFBYSxVQUFVLFFBQVE7QUFDeEM7QUFFTyxTQUFTSCxhQUFhRSxXQUErQjtJQUMxRCxNQUFNTSxTQUFTQyxDQUFBQSxHQUFBQSx1QkFBQUEscUJBQXFCLEVBQUNQO0lBQ3JDLE1BQU1DLFdBQVdGLGtCQUFrQkMsZUFBZTtJQUVsRCxJQUFJSSxJQUFJSSxRQUFRLENBQUNGLFNBQVM7UUFDeEIsK0RBQStEO1FBQy9ELHFDQUFxQztRQUNyQyxPQUFPQSxPQUFPRyxPQUFPLENBQUMsU0FBUztJQUNqQztJQUVBLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxJQUFJLEVBQUUsR0FBR1QsT0FBT0MsUUFBUTtJQUMxQyxPQUFPRixXQUFZLE9BQUlTLFdBQVdDLENBQUFBLE9BQU8sTUFBSUEsT0FBUyxNQUFLTDtBQUM3RCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvZ2V0LXNvY2tldC11cmwudHM/N2ZiMCJdLCJuYW1lcyI6WyJnZXRTb2NrZXRVcmwiLCJnZXRTb2NrZXRQcm90b2NvbCIsImFzc2V0UHJlZml4IiwicHJvdG9jb2wiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsIlVSTCIsImUiLCJwcmVmaXgiLCJub3JtYWxpemVkQXNzZXRQcmVmaXgiLCJjYW5QYXJzZSIsInJlcGxhY2UiLCJob3N0bmFtZSIsInBvcnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/get-socket-url.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js ***!
  \*******************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getErrorByType\", ({\n    enumerable: true,\n    get: function() {\n        return getErrorByType;\n    }\n}));\nconst _shared = __webpack_require__(/*! ../../shared */ \"./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _stackframe = __webpack_require__(/*! ./stack-frame */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js\");\nconst _errorsource = __webpack_require__(/*! ../../../../../shared/lib/error-source */ \"./node_modules/next/dist/shared/lib/error-source.js\");\nasync function getErrorByType(ev, isAppDir) {\n    const { id, event } = ev;\n    switch(event.type){\n        case _shared.ACTION_UNHANDLED_ERROR:\n        case _shared.ACTION_UNHANDLED_REJECTION:\n            {\n                const readyRuntimeError = {\n                    id,\n                    runtime: true,\n                    error: event.reason,\n                    frames: await (0, _stackframe.getOriginalStackFrames)(event.frames, (0, _errorsource.getErrorSource)(event.reason), isAppDir, event.reason.toString())\n                };\n                if (event.type === _shared.ACTION_UNHANDLED_ERROR) {\n                    readyRuntimeError.componentStackFrames = event.componentStackFrames;\n                }\n                return readyRuntimeError;\n            }\n        default:\n            {\n                break;\n            }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const _ = event;\n    throw new Error(\"type system invariant violation\");\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=getErrorByType.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvZ2V0RXJyb3JCeVR5cGUuanMiLCJtYXBwaW5ncyI6Ijs7OztrREFrQnNCQTs7O2VBQUFBOzs7b0NBZmY7d0NBRWdDO3lDQUdSO0FBVXhCLGVBQWVBLGVBQ3BCQyxFQUF1QixFQUN2QkMsUUFBaUI7SUFFakIsTUFBTSxFQUFFQyxFQUFFLEVBQUVDLEtBQUssRUFBRSxHQUFHSDtJQUN0QixPQUFRRyxNQUFNQyxJQUFJO1FBQ2hCLEtBQUtDLFFBQUFBLHNCQUFzQjtRQUMzQixLQUFLQyxRQUFBQSwwQkFBMEI7WUFBRTtnQkFDL0IsTUFBTUMsb0JBQXVDO29CQUMzQ0w7b0JBQ0FNLFNBQVM7b0JBQ1RDLE9BQU9OLE1BQU1PLE1BQU07b0JBQ25CQyxRQUFRLE1BQU1DLENBQUFBLEdBQUFBLFlBQUFBLHNCQUFzQixFQUNsQ1QsTUFBTVEsTUFBTSxFQUNaRSxDQUFBQSxHQUFBQSxhQUFBQSxjQUFjLEVBQUNWLE1BQU1PLE1BQU0sR0FDM0JULFVBQ0FFLE1BQU1PLE1BQU0sQ0FBQ0ksUUFBUTtnQkFFekI7Z0JBQ0EsSUFBSVgsTUFBTUMsSUFBSSxLQUFLQyxRQUFBQSxzQkFBc0IsRUFBRTtvQkFDekNFLGtCQUFrQlEsb0JBQW9CLEdBQUdaLE1BQU1ZLG9CQUFvQjtnQkFDckU7Z0JBQ0EsT0FBT1I7WUFDVDtRQUNBO1lBQVM7Z0JBQ1A7WUFDRjtJQUNGO0lBQ0EsNkRBQTZEO0lBQzdELE1BQU1TLElBQVdiO0lBQ2pCLE1BQU0sSUFBSWMsTUFBTTtBQUNsQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvZ2V0RXJyb3JCeVR5cGUudHM/YWI3ZSJdLCJuYW1lcyI6WyJnZXRFcnJvckJ5VHlwZSIsImV2IiwiaXNBcHBEaXIiLCJpZCIsImV2ZW50IiwidHlwZSIsIkFDVElPTl9VTkhBTkRMRURfRVJST1IiLCJBQ1RJT05fVU5IQU5ETEVEX1JFSkVDVElPTiIsInJlYWR5UnVudGltZUVycm9yIiwicnVudGltZSIsImVycm9yIiwicmVhc29uIiwiZnJhbWVzIiwiZ2V0T3JpZ2luYWxTdGFja0ZyYW1lcyIsImdldEVycm9yU291cmNlIiwidG9TdHJpbmciLCJjb21wb25lbnRTdGFja0ZyYW1lcyIsIl8iLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/group-stack-frames-by-framework.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/group-stack-frames-by-framework.js ***!
  \************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"groupStackFramesByFramework\", ({\n    enumerable: true,\n    get: function() {\n        return groupStackFramesByFramework;\n    }\n}));\nfunction groupStackFramesByFramework(stackFrames) {\n    const stackFramesGroupedByFramework = [];\n    for (const stackFrame of stackFrames){\n        const currentGroup = stackFramesGroupedByFramework[stackFramesGroupedByFramework.length - 1];\n        const framework = stackFrame.sourcePackage;\n        if (currentGroup && currentGroup.framework === framework) {\n            currentGroup.stackFrames.push(stackFrame);\n        } else {\n            stackFramesGroupedByFramework.push({\n                framework: framework,\n                stackFrames: [\n                    stackFrame\n                ]\n            });\n        }\n    }\n    return stackFramesGroupedByFramework;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=group-stack-frames-by-framework.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvZ3JvdXAtc3RhY2stZnJhbWVzLWJ5LWZyYW1ld29yay5qcyIsIm1hcHBpbmdzIjoiOzs7OytEQTZCZ0JBOzs7ZUFBQUE7OztBQUFULFNBQVNBLDRCQUNkQyxXQUFpQztJQUVqQyxNQUFNQyxnQ0FBb0QsRUFBRTtJQUU1RCxLQUFLLE1BQU1DLGNBQWNGLFlBQWE7UUFDcEMsTUFBTUcsZUFDSkYsNkJBQTZCLENBQUNBLDhCQUE4QkcsTUFBTSxHQUFHLEVBQUU7UUFDekUsTUFBTUMsWUFBWUgsV0FBV0ksYUFBYTtRQUUxQyxJQUFJSCxnQkFBZ0JBLGFBQWFFLFNBQVMsS0FBS0EsV0FBVztZQUN4REYsYUFBYUgsV0FBVyxDQUFDTyxJQUFJLENBQUNMO1FBQ2hDLE9BQU87WUFDTEQsOEJBQThCTSxJQUFJLENBQUM7Z0JBQ2pDRixXQUFXQTtnQkFDWEwsYUFBYTtvQkFBQ0U7aUJBQVc7WUFDM0I7UUFDRjtJQUNGO0lBRUEsT0FBT0Q7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvZ3JvdXAtc3RhY2stZnJhbWVzLWJ5LWZyYW1ld29yay50cz81ZDVhIl0sIm5hbWVzIjpbImdyb3VwU3RhY2tGcmFtZXNCeUZyYW1ld29yayIsInN0YWNrRnJhbWVzIiwic3RhY2tGcmFtZXNHcm91cGVkQnlGcmFtZXdvcmsiLCJzdGFja0ZyYW1lIiwiY3VycmVudEdyb3VwIiwibGVuZ3RoIiwiZnJhbWV3b3JrIiwic291cmNlUGFja2FnZSIsInB1c2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/group-stack-frames-by-framework.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/get-socket-url.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/get-socket-url.js ***!
  \*******************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getSocketUrl\", ({\n    enumerable: true,\n    get: function() {\n        return getSocketUrl;\n    }\n}));\nconst _normalizedassetprefix = __webpack_require__(/*! ../../../../../shared/lib/normalized-asset-prefix */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/normalized-asset-prefix.js\");\nfunction getSocketProtocol(assetPrefix) {\n    let protocol = window.location.protocol;\n    try {\n        // assetPrefix is a url\n        protocol = new URL(assetPrefix).protocol;\n    } catch (e) {}\n    return protocol === \"http:\" ? \"ws:\" : \"wss:\";\n}\nfunction getSocketUrl(assetPrefix) {\n    const prefix = (0, _normalizedassetprefix.normalizedAssetPrefix)(assetPrefix);\n    const protocol = getSocketProtocol(assetPrefix || \"\");\n    if (URL.canParse(prefix)) {\n        // since normalized asset prefix is ensured to be a URL format,\n        // we can safely replace the protocol\n        return prefix.replace(/^http/, \"ws\");\n    }\n    const { hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? \":\" + port : \"\") + prefix;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-socket-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9nZXQtc29ja2V0LXVybC5qcyIsIm1hcHBpbmdzIjoiOzs7O2dEQWFnQkE7OztlQUFBQTs7O21EQWJzQjtBQUV0QyxTQUFTQyxrQkFBa0JDLFdBQW1CO0lBQzVDLElBQUlDLFdBQVdDLE9BQU9DLFFBQVEsQ0FBQ0YsUUFBUTtJQUV2QyxJQUFJO1FBQ0YsdUJBQXVCO1FBQ3ZCQSxXQUFXLElBQUlHLElBQUlKLGFBQWFDLFFBQVE7SUFDMUMsRUFBRSxPQUFBSSxHQUFNLENBQUM7SUFFVCxPQUFPSixhQUFhLFVBQVUsUUFBUTtBQUN4QztBQUVPLFNBQVNILGFBQWFFLFdBQStCO0lBQzFELE1BQU1NLFNBQVNDLENBQUFBLEdBQUFBLHVCQUFBQSxxQkFBcUIsRUFBQ1A7SUFDckMsTUFBTUMsV0FBV0Ysa0JBQWtCQyxlQUFlO0lBRWxELElBQUlJLElBQUlJLFFBQVEsQ0FBQ0YsU0FBUztRQUN4QiwrREFBK0Q7UUFDL0QscUNBQXFDO1FBQ3JDLE9BQU9BLE9BQU9HLE9BQU8sQ0FBQyxTQUFTO0lBQ2pDO0lBRUEsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLElBQUksRUFBRSxHQUFHVCxPQUFPQyxRQUFRO0lBQzFDLE9BQU9GLFdBQVksT0FBSVMsV0FBV0MsQ0FBQUEsT0FBTyxNQUFJQSxPQUFTLE1BQUtMO0FBQzdEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9nZXQtc29ja2V0LXVybC50cz83ZmIwIl0sIm5hbWVzIjpbImdldFNvY2tldFVybCIsImdldFNvY2tldFByb3RvY29sIiwiYXNzZXRQcmVmaXgiLCJwcm90b2NvbCIsIndpbmRvdyIsImxvY2F0aW9uIiwiVVJMIiwiZSIsInByZWZpeCIsIm5vcm1hbGl6ZWRBc3NldFByZWZpeCIsImNhblBhcnNlIiwicmVwbGFjZSIsImhvc3RuYW1lIiwicG9ydCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/get-socket-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js ***!
  \*******************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getErrorByType\", ({\n    enumerable: true,\n    get: function() {\n        return getErrorByType;\n    }\n}));\nconst _shared = __webpack_require__(/*! ../../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _stackframe = __webpack_require__(/*! ./stack-frame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js\");\nconst _errorsource = __webpack_require__(/*! ../../../../../shared/lib/error-source */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/error-source.js\");\nasync function getErrorByType(ev, isAppDir) {\n    const { id, event } = ev;\n    switch(event.type){\n        case _shared.ACTION_UNHANDLED_ERROR:\n        case _shared.ACTION_UNHANDLED_REJECTION:\n            {\n                const readyRuntimeError = {\n                    id,\n                    runtime: true,\n                    error: event.reason,\n                    frames: await (0, _stackframe.getOriginalStackFrames)(event.frames, (0, _errorsource.getErrorSource)(event.reason), isAppDir, event.reason.toString())\n                };\n                if (event.type === _shared.ACTION_UNHANDLED_ERROR) {\n                    readyRuntimeError.componentStackFrames = event.componentStackFrames;\n                }\n                return readyRuntimeError;\n            }\n        default:\n            {\n                break;\n            }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const _ = event;\n    throw new Error(\"type system invariant violation\");\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=getErrorByType.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9nZXRFcnJvckJ5VHlwZS5qcyIsIm1hcHBpbmdzIjoiOzs7O2tEQWtCc0JBOzs7ZUFBQUE7OztvQ0FmZjt3Q0FFZ0M7eUNBR1I7QUFVeEIsZUFBZUEsZUFDcEJDLEVBQXVCLEVBQ3ZCQyxRQUFpQjtJQUVqQixNQUFNLEVBQUVDLEVBQUUsRUFBRUMsS0FBSyxFQUFFLEdBQUdIO0lBQ3RCLE9BQVFHLE1BQU1DLElBQUk7UUFDaEIsS0FBS0MsUUFBQUEsc0JBQXNCO1FBQzNCLEtBQUtDLFFBQUFBLDBCQUEwQjtZQUFFO2dCQUMvQixNQUFNQyxvQkFBdUM7b0JBQzNDTDtvQkFDQU0sU0FBUztvQkFDVEMsT0FBT04sTUFBTU8sTUFBTTtvQkFDbkJDLFFBQVEsTUFBTUMsQ0FBQUEsR0FBQUEsWUFBQUEsc0JBQXNCLEVBQ2xDVCxNQUFNUSxNQUFNLEVBQ1pFLENBQUFBLEdBQUFBLGFBQUFBLGNBQWMsRUFBQ1YsTUFBTU8sTUFBTSxHQUMzQlQsVUFDQUUsTUFBTU8sTUFBTSxDQUFDSSxRQUFRO2dCQUV6QjtnQkFDQSxJQUFJWCxNQUFNQyxJQUFJLEtBQUtDLFFBQUFBLHNCQUFzQixFQUFFO29CQUN6Q0Usa0JBQWtCUSxvQkFBb0IsR0FBR1osTUFBTVksb0JBQW9CO2dCQUNyRTtnQkFDQSxPQUFPUjtZQUNUO1FBQ0E7WUFBUztnQkFDUDtZQUNGO0lBQ0Y7SUFDQSw2REFBNkQ7SUFDN0QsTUFBTVMsSUFBV2I7SUFDakIsTUFBTSxJQUFJYyxNQUFNO0FBQ2xCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9nZXRFcnJvckJ5VHlwZS50cz9hYjdlIl0sIm5hbWVzIjpbImdldEVycm9yQnlUeXBlIiwiZXYiLCJpc0FwcERpciIsImlkIiwiZXZlbnQiLCJ0eXBlIiwiQUNUSU9OX1VOSEFORExFRF9FUlJPUiIsIkFDVElPTl9VTkhBTkRMRURfUkVKRUNUSU9OIiwicmVhZHlSdW50aW1lRXJyb3IiLCJydW50aW1lIiwiZXJyb3IiLCJyZWFzb24iLCJmcmFtZXMiLCJnZXRPcmlnaW5hbFN0YWNrRnJhbWVzIiwiZ2V0RXJyb3JTb3VyY2UiLCJ0b1N0cmluZyIsImNvbXBvbmVudFN0YWNrRnJhbWVzIiwiXyIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/group-stack-frames-by-framework.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/group-stack-frames-by-framework.js ***!
  \************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"groupStackFramesByFramework\", ({\n    enumerable: true,\n    get: function() {\n        return groupStackFramesByFramework;\n    }\n}));\nfunction groupStackFramesByFramework(stackFrames) {\n    const stackFramesGroupedByFramework = [];\n    for (const stackFrame of stackFrames){\n        const currentGroup = stackFramesGroupedByFramework[stackFramesGroupedByFramework.length - 1];\n        const framework = stackFrame.sourcePackage;\n        if (currentGroup && currentGroup.framework === framework) {\n            currentGroup.stackFrames.push(stackFrame);\n        } else {\n            stackFramesGroupedByFramework.push({\n                framework: framework,\n                stackFrames: [\n                    stackFrame\n                ]\n            });\n        }\n    }\n    return stackFramesGroupedByFramework;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=group-stack-frames-by-framework.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9ncm91cC1zdGFjay1mcmFtZXMtYnktZnJhbWV3b3JrLmpzIiwibWFwcGluZ3MiOiI7Ozs7K0RBNkJnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0EsNEJBQ2RDLFdBQWlDO0lBRWpDLE1BQU1DLGdDQUFvRCxFQUFFO0lBRTVELEtBQUssTUFBTUMsY0FBY0YsWUFBYTtRQUNwQyxNQUFNRyxlQUNKRiw2QkFBNkIsQ0FBQ0EsOEJBQThCRyxNQUFNLEdBQUcsRUFBRTtRQUN6RSxNQUFNQyxZQUFZSCxXQUFXSSxhQUFhO1FBRTFDLElBQUlILGdCQUFnQkEsYUFBYUUsU0FBUyxLQUFLQSxXQUFXO1lBQ3hERixhQUFhSCxXQUFXLENBQUNPLElBQUksQ0FBQ0w7UUFDaEMsT0FBTztZQUNMRCw4QkFBOEJNLElBQUksQ0FBQztnQkFDakNGLFdBQVdBO2dCQUNYTCxhQUFhO29CQUFDRTtpQkFBVztZQUMzQjtRQUNGO0lBQ0Y7SUFFQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9ncm91cC1zdGFjay1mcmFtZXMtYnktZnJhbWV3b3JrLnRzPzVkNWEiXSwibmFtZXMiOlsiZ3JvdXBTdGFja0ZyYW1lc0J5RnJhbWV3b3JrIiwic3RhY2tGcmFtZXMiLCJzdGFja0ZyYW1lc0dyb3VwZWRCeUZyYW1ld29yayIsInN0YWNrRnJhbWUiLCJjdXJyZW50R3JvdXAiLCJsZW5ndGgiLCJmcmFtZXdvcmsiLCJzb3VyY2VQYWNrYWdlIiwicHVzaCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/group-stack-frames-by-framework.js\n"));

/***/ })

}]);