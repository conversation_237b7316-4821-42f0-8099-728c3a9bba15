"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("framework-node_modules_next_dist_client_components_react-dev-overlay_internal_styles_B",{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/Base.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/Base.js ***!
  \********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Base\", ({\n    enumerable: true,\n    get: function() {\n        return Base;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _nooptemplate = __webpack_require__(/*! ../helpers/noop-template */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n        :host {\\n          --size-gap-half: 4px;\\n          --size-gap: 8px;\\n          --size-gap-double: 16px;\\n          --size-gap-triple: 24px;\\n          --size-gap-quad: 32px;\\n\\n          --size-font-small: 14px;\\n          --size-font: 16px;\\n          --size-font-big: 20px;\\n          --size-font-bigger: 24px;\\n\\n          --color-background: white;\\n          --color-font: #757575;\\n          --color-backdrop: rgba(17, 17, 17, 0.2);\\n\\n          --color-title-color: #1f1f1f;\\n          --color-stack-h6: #222;\\n          --color-stack-headline: #666;\\n          --color-stack-subline: #999;\\n          --color-stack-notes: #777;\\n\\n          --color-accents-1: #808080;\\n          --color-accents-2: #222222;\\n          --color-accents-3: #404040;\\n\\n          --color-text-color-red-1: #ff5555;\\n          --color-text-background-red-1: #fff9f9;\\n\\n          --font-stack-monospace: 'SFMono-Regular', Consolas, 'Liberation Mono',\\n            Menlo, Courier, monospace;\\n          --font-stack-sans: -apple-system, 'Source Sans Pro', sans-serif;\\n\\n          --color-ansi-selection: rgba(95, 126, 151, 0.48);\\n          --color-ansi-bg: #111111;\\n          --color-ansi-fg: #cccccc;\\n\\n          --color-ansi-white: #777777;\\n          --color-ansi-black: #141414;\\n          --color-ansi-blue: #00aaff;\\n          --color-ansi-cyan: #88ddff;\\n          --color-ansi-green: #98ec65;\\n          --color-ansi-magenta: #aa88ff;\\n          --color-ansi-red: #ff5555;\\n          --color-ansi-yellow: #ffcc33;\\n          --color-ansi-bright-white: #ffffff;\\n          --color-ansi-bright-black: #777777;\\n          --color-ansi-bright-blue: #33bbff;\\n          --color-ansi-bright-cyan: #bbecff;\\n          --color-ansi-bright-green: #b6f292;\\n          --color-ansi-bright-magenta: #cebbff;\\n          --color-ansi-bright-red: #ff8888;\\n          --color-ansi-bright-yellow: #ffd966;\\n        }\\n\\n        @media (prefers-color-scheme: dark) {\\n          :host {\\n            --color-background: rgb(28, 28, 30);\\n            --color-font: white;\\n            --color-backdrop: rgb(44, 44, 46);\\n\\n            --color-title-color: #fafafa;\\n            --color-stack-h6: rgb(200, 200, 204);\\n            --color-stack-headline: rgb(99, 99, 102);\\n            --color-stack-notes: #a9a9a9;\\n            --color-stack-subline: rgb(121, 121, 121);\\n\\n            --color-accents-3: rgb(118, 118, 118);\\n\\n            --color-text-background-red-1: #2a1e1e;\\n          }\\n        }\\n\\n        .mono {\\n          font-family: var(--font-stack-monospace);\\n        }\\n\\n        h1,\\n        h2,\\n        h3,\\n        h4,\\n        h5,\\n        h6 {\\n          margin-bottom: var(--size-gap);\\n          font-weight: 500;\\n          line-height: 1.5;\\n        }\\n      \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction Base() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _nooptemplate.noop)(_templateObject())\n    });\n}\n_c = Base;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=Base.js.map\nvar _c;\n$RefreshReg$(_c, \"Base\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL3N0eWxlcy9CYXNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7d0NBR2dCQTs7O2VBQUFBOzs7Ozs7NkVBSE87MENBQ0s7Ozs7Ozs7Ozs7QUFFckIsU0FBU0E7SUFDZCxPQUNFLFdBREYsR0FDRSxJQUFBQyxZQUFBQyxHQUFBLEVBQUNDLFNBQUFBO3NCQUNFQyxjQUFBQSxJQUFHLEVBQUFDOztBQTJGVjtLQTlGZ0JMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvc3R5bGVzL0Jhc2UudHN4PzFmYjAiXSwibmFtZXMiOlsiQmFzZSIsIl9qc3hydW50aW1lIiwianN4Iiwic3R5bGUiLCJjc3MiLCJfdGVtcGxhdGVPYmplY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/Base.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/ComponentStyles.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/ComponentStyles.js ***!
  \*******************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ComponentStyles\", ({\n    enumerable: true,\n    get: function() {\n        return ComponentStyles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _styles = __webpack_require__(/*! ../components/CodeFrame/styles */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/styles.js\");\nconst _Dialog = __webpack_require__(/*! ../components/Dialog */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js\");\nconst _styles1 = __webpack_require__(/*! ../components/LeftRightDialogHeader/styles */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/styles.js\");\nconst _styles2 = __webpack_require__(/*! ../components/Overlay/styles */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/styles.js\");\nconst _styles3 = __webpack_require__(/*! ../components/Terminal/styles */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Terminal/styles.js\");\nconst _Toast = __webpack_require__(/*! ../components/Toast */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Toast/index.js\");\nconst _VersionStalenessInfo = __webpack_require__(/*! ../components/VersionStalenessInfo */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/index.js\");\nconst _BuildError = __webpack_require__(/*! ../container/BuildError */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/container/BuildError.js\");\nconst _Errors = __webpack_require__(/*! ../container/Errors */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/container/Errors.js\");\nconst _RuntimeError = __webpack_require__(/*! ../container/RuntimeError */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/index.js\");\nconst _nooptemplate = __webpack_require__(/*! ../helpers/noop-template */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n      \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction ComponentStyles() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _nooptemplate.noop)(_templateObject(), _styles2.styles, _Toast.styles, _Dialog.styles, _styles1.styles, _styles.styles, _styles3.styles, _BuildError.styles, _Errors.styles, _RuntimeError.styles, _VersionStalenessInfo.styles)\n    });\n}\n_c = ComponentStyles;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=ComponentStyles.js.map\nvar _c;\n$RefreshReg$(_c, \"ComponentStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL3N0eWxlcy9Db21wb25lbnRTdHlsZXMuanMiLCJtYXBwaW5ncyI6Ijs7OzttREFZZ0JBOzs7ZUFBQUE7Ozs7O29DQVpvQjtvQ0FDSDtxQ0FDZTtxQ0FDZDtxQ0FDQzttQ0FDSDtrREFDVzt3Q0FDQTtvQ0FDSTswQ0FDTzswQ0FDMUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXJCLFNBQVNBO0lBQ2QsT0FDRSxXQURGLEdBQ0UsSUFBQUMsWUFBQUMsR0FBQSxFQUFDQyxTQUFBQTtzQkFDRUMsY0FBQUEsSUFBRyxFQUFBQyxtQkFDQUMsU0FBQUEsTUFBTyxFQUNQQyxPQUFBQSxNQUFLLEVBQ0xDLFFBQUFBLE1BQU0sRUFDTkMsU0FBQUEsTUFBcUIsRUFDckJDLFFBQUFBLE1BQVMsRUFDVEMsU0FBQUEsTUFBUSxFQUNSQyxZQUFBQSxNQUFnQixFQUNoQkMsUUFBQUEsTUFBb0IsRUFDcEJDLGNBQUFBLE1BQTJCLEVBQzNCQyxzQkFBQUEsTUFBZ0I7O0FBSTFCO0tBakJnQmYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9zdHlsZXMvQ29tcG9uZW50U3R5bGVzLnRzeD8wOTZjIl0sIm5hbWVzIjpbIkNvbXBvbmVudFN0eWxlcyIsIl9qc3hydW50aW1lIiwianN4Iiwic3R5bGUiLCJjc3MiLCJfdGVtcGxhdGVPYmplY3QiLCJvdmVybGF5IiwidG9hc3QiLCJkaWFsb2ciLCJsZWZ0UmlnaHREaWFsb2dIZWFkZXIiLCJjb2RlRnJhbWUiLCJ0ZXJtaW5hbCIsImJ1aWxkRXJyb3JTdHlsZXMiLCJjb250YWluZXJFcnJvclN0eWxlcyIsImNvbnRhaW5lclJ1bnRpbWVFcnJvclN0eWxlcyIsInZlcnNpb25TdGFsZW5lc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/ComponentStyles.js\n"));

/***/ })

});