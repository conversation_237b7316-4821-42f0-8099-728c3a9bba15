"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Dialog_i"],{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js ***!
  \********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Dialog: function() {\n        return _Dialog.Dialog;\n    },\n    DialogBody: function() {\n        return _DialogBody.DialogBody;\n    },\n    DialogContent: function() {\n        return _DialogContent.DialogContent;\n    },\n    DialogHeader: function() {\n        return _DialogHeader.DialogHeader;\n    },\n    styles: function() {\n        return _styles.styles;\n    }\n});\nconst _Dialog = __webpack_require__(/*! ./Dialog */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/Dialog.js\");\nconst _DialogBody = __webpack_require__(/*! ./DialogBody */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogBody.js\");\nconst _DialogContent = __webpack_require__(/*! ./DialogContent */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogContent.js\");\nconst _DialogHeader = __webpack_require__(/*! ./DialogHeader */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogHeader.js\");\nconst _styles = __webpack_require__(/*! ./styles */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvRGlhbG9nL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQUFTQSxRQUFNO2VBQU5BLFFBQUFBLE1BQU07O0lBQ05DLFlBQVU7ZUFBVkEsWUFBQUEsVUFBVTs7SUFDVkMsZUFBYTtlQUFiQSxlQUFBQSxhQUFhOztJQUNiQyxjQUFZO2VBQVpBLGNBQUFBLFlBQVk7O0lBQ1pDLFFBQU07ZUFBTkEsUUFBQUEsTUFBTTs7O29DQUpRO3dDQUNJOzJDQUNHOzBDQUNEO29DQUNOIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9EaWFsb2cvaW5kZXgudHM/YzA4OSJdLCJuYW1lcyI6WyJEaWFsb2ciLCJEaWFsb2dCb2R5IiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0hlYWRlciIsInN0eWxlcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js ***!
  \*********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _nooptemplate = __webpack_require__(/*! ../../helpers/noop-template */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  [data-nextjs-dialog] {\\n    display: flex;\\n    flex-direction: column;\\n    width: 100%;\\n    margin-right: auto;\\n    margin-left: auto;\\n    outline: none;\\n    background: var(--color-background);\\n    border-radius: var(--size-gap);\\n    box-shadow: 0 var(--size-gap-half) var(--size-gap-double)\\n      rgba(0, 0, 0, 0.25);\\n    max-height: calc(100% - 56px);\\n    overflow-y: hidden;\\n  }\\n\\n  @media (max-height: 812px) {\\n    [data-nextjs-dialog-overlay] {\\n      max-height: calc(100% - 15px);\\n    }\\n  }\\n\\n  @media (min-width: 576px) {\\n    [data-nextjs-dialog] {\\n      max-width: 540px;\\n      box-shadow: 0 var(--size-gap) var(--size-gap-quad) rgba(0, 0, 0, 0.25);\\n    }\\n  }\\n\\n  @media (min-width: 768px) {\\n    [data-nextjs-dialog] {\\n      max-width: 720px;\\n    }\\n  }\\n\\n  @media (min-width: 992px) {\\n    [data-nextjs-dialog] {\\n      max-width: 960px;\\n    }\\n  }\\n\\n  [data-nextjs-dialog-banner] {\\n    position: relative;\\n  }\\n  [data-nextjs-dialog-banner].banner-warning {\\n    border-color: var(--color-ansi-yellow);\\n  }\\n  [data-nextjs-dialog-banner].banner-error {\\n    border-color: var(--color-ansi-red);\\n  }\\n\\n  [data-nextjs-dialog-banner]::after {\\n    z-index: 2;\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    right: 0;\\n    width: 100%;\\n    /* banner width: */\\n    border-top-width: var(--size-gap-half);\\n    border-bottom-width: 0;\\n    border-top-style: solid;\\n    border-bottom-style: solid;\\n    border-top-color: inherit;\\n    border-bottom-color: transparent;\\n  }\\n\\n  [data-nextjs-dialog-content] {\\n    overflow-y: auto;\\n    border: none;\\n    margin: 0;\\n    /* calc(padding + banner width offset) */\\n    padding: calc(var(--size-gap-double) + var(--size-gap-half))\\n      var(--size-gap-double);\\n    height: 100%;\\n    display: flex;\\n    flex-direction: column;\\n  }\\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-header] {\\n    flex-shrink: 0;\\n    margin-bottom: var(--size-gap-double);\\n  }\\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-body] {\\n    position: relative;\\n    flex: 1 1 auto;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvRGlhbG9nL3N0eWxlcy5qcyIsIm1hcHBpbmdzIjoiOzs7OzBDQTBGU0E7OztlQUFBQTs7OzswQ0ExRm1COzs7Ozs7Ozs7O0FBRTVCLE1BQU1BLFNBQUFBLENBQUFBLEdBQVNDLGNBQUFBLElBQUcsRUFBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9jb21wb25lbnRzL0RpYWxvZy9zdHlsZXMudHM/MDY5NSJdLCJuYW1lcyI6WyJzdHlsZXMiLCJjc3MiLCJfdGVtcGxhdGVPYmplY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js ***!
  \********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Dialog: function() {\n        return _Dialog.Dialog;\n    },\n    DialogBody: function() {\n        return _DialogBody.DialogBody;\n    },\n    DialogContent: function() {\n        return _DialogContent.DialogContent;\n    },\n    DialogHeader: function() {\n        return _DialogHeader.DialogHeader;\n    },\n    styles: function() {\n        return _styles.styles;\n    }\n});\nconst _Dialog = __webpack_require__(/*! ./Dialog */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/Dialog.js\");\nconst _DialogBody = __webpack_require__(/*! ./DialogBody */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogBody.js\");\nconst _DialogContent = __webpack_require__(/*! ./DialogContent */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogContent.js\");\nconst _DialogHeader = __webpack_require__(/*! ./DialogHeader */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogHeader.js\");\nconst _styles = __webpack_require__(/*! ./styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9EaWFsb2cvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBQVNBLFFBQU07ZUFBTkEsUUFBQUEsTUFBTTs7SUFDTkMsWUFBVTtlQUFWQSxZQUFBQSxVQUFVOztJQUNWQyxlQUFhO2VBQWJBLGVBQUFBLGFBQWE7O0lBQ2JDLGNBQVk7ZUFBWkEsY0FBQUEsWUFBWTs7SUFDWkMsUUFBTTtlQUFOQSxRQUFBQSxNQUFNOzs7b0NBSlE7d0NBQ0k7MkNBQ0c7MENBQ0Q7b0NBQ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9jb21wb25lbnRzL0RpYWxvZy9pbmRleC50cz9jMDg5Il0sIm5hbWVzIjpbIkRpYWxvZyIsIkRpYWxvZ0JvZHkiLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nSGVhZGVyIiwic3R5bGVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js ***!
  \*********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _nooptemplate = __webpack_require__(/*! ../../helpers/noop-template */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  [data-nextjs-dialog] {\\n    display: flex;\\n    flex-direction: column;\\n    width: 100%;\\n    margin-right: auto;\\n    margin-left: auto;\\n    outline: none;\\n    background: var(--color-background);\\n    border-radius: var(--size-gap);\\n    box-shadow: 0 var(--size-gap-half) var(--size-gap-double)\\n      rgba(0, 0, 0, 0.25);\\n    max-height: calc(100% - 56px);\\n    overflow-y: hidden;\\n  }\\n\\n  @media (max-height: 812px) {\\n    [data-nextjs-dialog-overlay] {\\n      max-height: calc(100% - 15px);\\n    }\\n  }\\n\\n  @media (min-width: 576px) {\\n    [data-nextjs-dialog] {\\n      max-width: 540px;\\n      box-shadow: 0 var(--size-gap) var(--size-gap-quad) rgba(0, 0, 0, 0.25);\\n    }\\n  }\\n\\n  @media (min-width: 768px) {\\n    [data-nextjs-dialog] {\\n      max-width: 720px;\\n    }\\n  }\\n\\n  @media (min-width: 992px) {\\n    [data-nextjs-dialog] {\\n      max-width: 960px;\\n    }\\n  }\\n\\n  [data-nextjs-dialog-banner] {\\n    position: relative;\\n  }\\n  [data-nextjs-dialog-banner].banner-warning {\\n    border-color: var(--color-ansi-yellow);\\n  }\\n  [data-nextjs-dialog-banner].banner-error {\\n    border-color: var(--color-ansi-red);\\n  }\\n\\n  [data-nextjs-dialog-banner]::after {\\n    z-index: 2;\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    right: 0;\\n    width: 100%;\\n    /* banner width: */\\n    border-top-width: var(--size-gap-half);\\n    border-bottom-width: 0;\\n    border-top-style: solid;\\n    border-bottom-style: solid;\\n    border-top-color: inherit;\\n    border-bottom-color: transparent;\\n  }\\n\\n  [data-nextjs-dialog-content] {\\n    overflow-y: auto;\\n    border: none;\\n    margin: 0;\\n    /* calc(padding + banner width offset) */\\n    padding: calc(var(--size-gap-double) + var(--size-gap-half))\\n      var(--size-gap-double);\\n    height: 100%;\\n    display: flex;\\n    flex-direction: column;\\n  }\\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-header] {\\n    flex-shrink: 0;\\n    margin-bottom: var(--size-gap-double);\\n  }\\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-body] {\\n    position: relative;\\n    flex: 1 1 auto;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9EaWFsb2cvc3R5bGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7MENBMEZTQTs7O2VBQUFBOzs7OzBDQTFGbUI7Ozs7Ozs7Ozs7QUFFNUIsTUFBTUEsU0FBQUEsQ0FBQUEsR0FBU0MsY0FBQUEsSUFBRyxFQUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvRGlhbG9nL3N0eWxlcy50cz8wNjk1Il0sIm5hbWVzIjpbInN0eWxlcyIsImNzcyIsIl90ZW1wbGF0ZU9iamVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js\n"));

/***/ })

}]);