"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_p"],{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parse-component-stack.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parse-component-stack.js ***!
  \**************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"parseComponentStack\", ({\n    enumerable: true,\n    get: function() {\n        return parseComponentStack;\n    }\n}));\nvar LocationType;\n(function(LocationType) {\n    LocationType[\"FILE\"] = \"file\";\n    LocationType[\"WEBPACK_INTERNAL\"] = \"webpack-internal\";\n    LocationType[\"HTTP\"] = \"http\";\n    LocationType[\"PROTOCOL_RELATIVE\"] = \"protocol-relative\";\n    LocationType[\"UNKNOWN\"] = \"unknown\";\n})(LocationType || (LocationType = {}));\n/**\n * Get the type of frame line based on the location\n */ function getLocationType(location) {\n    if (location.startsWith(\"file://\")) {\n        return \"file\";\n    }\n    if (location.startsWith(\"webpack-internal://\")) {\n        return \"webpack-internal\";\n    }\n    if (location.startsWith(\"http://\") || location.startsWith(\"https://\")) {\n        return \"http\";\n    }\n    if (location.startsWith(\"//\")) {\n        return \"protocol-relative\";\n    }\n    return \"unknown\";\n}\nfunction parseStackFrameLocation(location) {\n    const locationType = getLocationType(location);\n    const modulePath = location == null ? void 0 : location.replace(/^(webpack-internal:\\/\\/\\/|file:\\/\\/)(\\(.*\\)\\/)?/, \"\");\n    var _modulePath_match;\n    const [, file, lineNumber, column] = (_modulePath_match = modulePath == null ? void 0 : modulePath.match(/^(.+):(\\d+):(\\d+)/)) != null ? _modulePath_match : [];\n    switch(locationType){\n        case \"file\":\n        case \"webpack-internal\":\n            return {\n                canOpenInEditor: true,\n                file,\n                lineNumber: lineNumber ? Number(lineNumber) : undefined,\n                column: column ? Number(column) : undefined\n            };\n        // When the location is a URL we only show the file\n        // TODO: Resolve http(s) URLs through sourcemaps\n        case \"http\":\n        case \"protocol-relative\":\n        case \"unknown\":\n        default:\n            {\n                return {\n                    canOpenInEditor: false\n                };\n            }\n    }\n}\nfunction parseComponentStack(componentStack) {\n    const componentStackFrames = [];\n    for (const line of componentStack.trim().split(\"\\n\")){\n        // Get component and file from the component stack line\n        const match = /at ([^ ]+)( \\((.*)\\))?/.exec(line);\n        if (match == null ? void 0 : match[1]) {\n            const component = match[1];\n            const location = match[3];\n            if (!location) {\n                componentStackFrames.push({\n                    canOpenInEditor: false,\n                    component\n                });\n                continue;\n            }\n            // Stop parsing the component stack if we reach a Next.js component\n            if (location == null ? void 0 : location.includes(\"next/dist\")) {\n                break;\n            }\n            const frameLocation = parseStackFrameLocation(location);\n            componentStackFrames.push({\n                component,\n                ...frameLocation\n            });\n        }\n    }\n    return componentStackFrames;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=parse-component-stack.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parse-component-stack.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parseStack.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parseStack.js ***!
  \***************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"parseStack\", ({\n    enumerable: true,\n    get: function() {\n        return parseStack;\n    }\n}));\nconst _stacktraceparser = __webpack_require__(/*! next/dist/compiled/stacktrace-parser */ \"./node_modules/next/dist/compiled/stacktrace-parser/stack-trace-parser.cjs.js\");\nconst regexNextStatic = /\\/_next(\\/static\\/.+)/;\nfunction parseStack(stack) {\n    const frames = (0, _stacktraceparser.parse)(stack);\n    return frames.map((frame)=>{\n        try {\n            const url = new URL(frame.file);\n            const res = regexNextStatic.exec(url.pathname);\n            if (res) {\n                var _process_env___NEXT_DIST_DIR_replace, _process_env___NEXT_DIST_DIR;\n                const distDir = (_process_env___NEXT_DIST_DIR = \"E:\\\\ankkorwoo\\\\ankkor\\\\.next\") == null ? void 0 : (_process_env___NEXT_DIST_DIR_replace = _process_env___NEXT_DIST_DIR.replace(/\\\\/g, \"/\")) == null ? void 0 : _process_env___NEXT_DIST_DIR_replace.replace(/\\/$/, \"\");\n                if (distDir) {\n                    frame.file = \"file://\" + distDir.concat(res.pop()) + url.search;\n                }\n            }\n        } catch (e) {}\n        return frame;\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=parseStack.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvcGFyc2VTdGFjay5qcyIsIm1hcHBpbmdzIjoiOzs7OzhDQUtnQkE7OztlQUFBQTs7OzhDQUxNO0FBR3RCLE1BQU1DLGtCQUFrQjtBQUVqQixTQUFTRCxXQUFXRSxLQUFhO0lBQ3RDLE1BQU1DLFNBQVNDLENBQUFBLEdBQUFBLGtCQUFBQSxLQUFLLEVBQUNGO0lBQ3JCLE9BQU9DLE9BQU9FLEdBQUcsQ0FBQyxDQUFDQztRQUNqQixJQUFJO1lBQ0YsTUFBTUMsTUFBTSxJQUFJQyxJQUFJRixNQUFNRyxJQUFJO1lBQzlCLE1BQU1DLE1BQU1ULGdCQUFnQlUsSUFBSSxDQUFDSixJQUFJSyxRQUFRO1lBQzdDLElBQUlGLEtBQUs7b0JBQ1NHLHNDQUFBQTtnQkFBaEIsTUFBTUMsVUFBQUEsQ0FBVUQsK0JBQUFBLDhCQUEyQixzQkFBM0JBLHVDQUFBQSw2QkFDWkksT0FBTyxDQUFDLE9BQU8seUJBREhKLHFDQUVaSSxPQUFPLENBQUMsT0FBTztnQkFDbkIsSUFBSUgsU0FBUztvQkFDWFIsTUFBTUcsSUFBSSxHQUFHLFlBQVlLLFFBQVFJLE1BQU0sQ0FBQ1IsSUFBSVMsR0FBRyxNQUFPWixJQUFJYSxNQUFNO2dCQUNsRTtZQUNGO1FBQ0YsRUFBRSxPQUFBQyxHQUFNLENBQUM7UUFDVCxPQUFPZjtJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9oZWxwZXJzL3BhcnNlU3RhY2sudHM/NzcyOSJdLCJuYW1lcyI6WyJwYXJzZVN0YWNrIiwicmVnZXhOZXh0U3RhdGljIiwic3RhY2siLCJmcmFtZXMiLCJwYXJzZSIsIm1hcCIsImZyYW1lIiwidXJsIiwiVVJMIiwiZmlsZSIsInJlcyIsImV4ZWMiLCJwYXRobmFtZSIsInByb2Nlc3MiLCJkaXN0RGlyIiwiZW52IiwiX19ORVhUX0RJU1RfRElSIiwicmVwbGFjZSIsImNvbmNhdCIsInBvcCIsInNlYXJjaCIsImUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parseStack.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/runtime-error-handler.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/runtime-error-handler.js ***!
  \**************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RuntimeErrorHandler\", ({\n    enumerable: true,\n    get: function() {\n        return RuntimeErrorHandler;\n    }\n}));\nconst RuntimeErrorHandler = {\n    hadRuntimeError: false\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=runtime-error-handler.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvcnVudGltZS1lcnJvci1oYW5kbGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7dURBQWFBOzs7ZUFBQUE7OztBQUFOLE1BQU1BLHNCQUFzQjtJQUNqQ0MsaUJBQWlCO0FBQ25CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9ydW50aW1lLWVycm9yLWhhbmRsZXIudHM/MWQwMyJdLCJuYW1lcyI6WyJSdW50aW1lRXJyb3JIYW5kbGVyIiwiaGFkUnVudGltZUVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/runtime-error-handler.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parse-component-stack.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parse-component-stack.js ***!
  \**************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"parseComponentStack\", ({\n    enumerable: true,\n    get: function() {\n        return parseComponentStack;\n    }\n}));\nvar LocationType;\n(function(LocationType) {\n    LocationType[\"FILE\"] = \"file\";\n    LocationType[\"WEBPACK_INTERNAL\"] = \"webpack-internal\";\n    LocationType[\"HTTP\"] = \"http\";\n    LocationType[\"PROTOCOL_RELATIVE\"] = \"protocol-relative\";\n    LocationType[\"UNKNOWN\"] = \"unknown\";\n})(LocationType || (LocationType = {}));\n/**\n * Get the type of frame line based on the location\n */ function getLocationType(location) {\n    if (location.startsWith(\"file://\")) {\n        return \"file\";\n    }\n    if (location.startsWith(\"webpack-internal://\")) {\n        return \"webpack-internal\";\n    }\n    if (location.startsWith(\"http://\") || location.startsWith(\"https://\")) {\n        return \"http\";\n    }\n    if (location.startsWith(\"//\")) {\n        return \"protocol-relative\";\n    }\n    return \"unknown\";\n}\nfunction parseStackFrameLocation(location) {\n    const locationType = getLocationType(location);\n    const modulePath = location == null ? void 0 : location.replace(/^(webpack-internal:\\/\\/\\/|file:\\/\\/)(\\(.*\\)\\/)?/, \"\");\n    var _modulePath_match;\n    const [, file, lineNumber, column] = (_modulePath_match = modulePath == null ? void 0 : modulePath.match(/^(.+):(\\d+):(\\d+)/)) != null ? _modulePath_match : [];\n    switch(locationType){\n        case \"file\":\n        case \"webpack-internal\":\n            return {\n                canOpenInEditor: true,\n                file,\n                lineNumber: lineNumber ? Number(lineNumber) : undefined,\n                column: column ? Number(column) : undefined\n            };\n        // When the location is a URL we only show the file\n        // TODO: Resolve http(s) URLs through sourcemaps\n        case \"http\":\n        case \"protocol-relative\":\n        case \"unknown\":\n        default:\n            {\n                return {\n                    canOpenInEditor: false\n                };\n            }\n    }\n}\nfunction parseComponentStack(componentStack) {\n    const componentStackFrames = [];\n    for (const line of componentStack.trim().split(\"\\n\")){\n        // Get component and file from the component stack line\n        const match = /at ([^ ]+)( \\((.*)\\))?/.exec(line);\n        if (match == null ? void 0 : match[1]) {\n            const component = match[1];\n            const location = match[3];\n            if (!location) {\n                componentStackFrames.push({\n                    canOpenInEditor: false,\n                    component\n                });\n                continue;\n            }\n            // Stop parsing the component stack if we reach a Next.js component\n            if (location == null ? void 0 : location.includes(\"next/dist\")) {\n                break;\n            }\n            const frameLocation = parseStackFrameLocation(location);\n            componentStackFrames.push({\n                component,\n                ...frameLocation\n            });\n        }\n    }\n    return componentStackFrames;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=parse-component-stack.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9wYXJzZS1jb21wb25lbnQtc3RhY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozt1REFxRWdCQTs7O2VBQUFBOzs7O1VBN0RYQyxZQUFBQTs7Ozs7O0dBQUFBLGdCQUFBQSxDQUFBQSxlQUFBQSxDQUFBQSxDQUFBQTtBQVFMOztDQUVDLEdBQ0QsU0FBU0MsZ0JBQWdCQyxRQUFnQjtJQUN2QyxJQUFJQSxTQUFTQyxVQUFVLENBQUMsWUFBWTtRQUNsQztJQUNGO0lBQ0EsSUFBSUQsU0FBU0MsVUFBVSxDQUFDLHdCQUF3QjtRQUM5QztJQUNGO0lBQ0EsSUFBSUQsU0FBU0MsVUFBVSxDQUFDLGNBQWNELFNBQVNDLFVBQVUsQ0FBQyxhQUFhO1FBQ3JFO0lBQ0Y7SUFDQSxJQUFJRCxTQUFTQyxVQUFVLENBQUMsT0FBTztRQUM3QjtJQUNGO0lBQ0E7QUFDRjtBQUVBLFNBQVNDLHdCQUNQRixRQUFnQjtJQUVoQixNQUFNRyxlQUFlSixnQkFBZ0JDO0lBRXJDLE1BQU1JLGFBQWFKLFlBQUFBLE9BQUFBLEtBQUFBLElBQUFBLFNBQVVLLE9BQU8sQ0FDbEMsbURBQ0E7UUFHQUQ7SUFERixNQUFNLEdBQUdFLE1BQU1DLFlBQVlDLE9BQU8sR0FDaENKLENBQUFBLG9CQUFBQSxjQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxXQUFZSyxLQUFLLENBQUMsZ0NBQWxCTCxvQkFBMEMsRUFBRTtJQUU5QyxPQUFRRDtRQUNOO1FBQ0E7WUFDRSxPQUFPO2dCQUNMTyxpQkFBaUI7Z0JBQ2pCSjtnQkFDQUMsWUFBWUEsYUFBYUksT0FBT0osY0FBY0s7Z0JBQzlDSixRQUFRQSxTQUFTRyxPQUFPSCxVQUFVSTtZQUNwQztRQUNGLG1EQUFtRDtRQUNuRCxnREFBZ0Q7UUFDaEQ7UUFDQTtRQUNBO1FBQ0E7WUFBUztnQkFDUCxPQUFPO29CQUNMRixpQkFBaUI7Z0JBQ25CO1lBQ0Y7SUFDRjtBQUNGO0FBRU8sU0FBU2Isb0JBQ2RnQixjQUFzQjtJQUV0QixNQUFNQyx1QkFBOEMsRUFBRTtJQUN0RCxLQUFLLE1BQU1DLFFBQVFGLGVBQWVHLElBQUksR0FBR0MsS0FBSyxDQUFDLE1BQU87UUFDcEQsdURBQXVEO1FBQ3ZELE1BQU1SLFFBQVEseUJBQXlCUyxJQUFJLENBQUNIO1FBQzVDLElBQUlOLFNBQUFBLE9BQUFBLEtBQUFBLElBQUFBLEtBQU8sQ0FBQyxFQUFFLEVBQUU7WUFDZCxNQUFNVSxZQUFZVixLQUFLLENBQUMsRUFBRTtZQUMxQixNQUFNVCxXQUFXUyxLQUFLLENBQUMsRUFBRTtZQUV6QixJQUFJLENBQUNULFVBQVU7Z0JBQ2JjLHFCQUFxQk0sSUFBSSxDQUFDO29CQUN4QlYsaUJBQWlCO29CQUNqQlM7Z0JBQ0Y7Z0JBQ0E7WUFDRjtZQUVBLG1FQUFtRTtZQUNuRSxJQUFJbkIsWUFBQUEsT0FBQUEsS0FBQUEsSUFBQUEsU0FBVXFCLFFBQVEsQ0FBQyxjQUFjO2dCQUNuQztZQUNGO1lBRUEsTUFBTUMsZ0JBQWdCcEIsd0JBQXdCRjtZQUM5Q2MscUJBQXFCTSxJQUFJLENBQUM7Z0JBQ3hCRDtnQkFDQSxHQUFHRyxhQUFhO1lBQ2xCO1FBQ0Y7SUFDRjtJQUVBLE9BQU9SO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9oZWxwZXJzL3BhcnNlLWNvbXBvbmVudC1zdGFjay50cz9kNGRiIl0sIm5hbWVzIjpbInBhcnNlQ29tcG9uZW50U3RhY2siLCJMb2NhdGlvblR5cGUiLCJnZXRMb2NhdGlvblR5cGUiLCJsb2NhdGlvbiIsInN0YXJ0c1dpdGgiLCJwYXJzZVN0YWNrRnJhbWVMb2NhdGlvbiIsImxvY2F0aW9uVHlwZSIsIm1vZHVsZVBhdGgiLCJyZXBsYWNlIiwiZmlsZSIsImxpbmVOdW1iZXIiLCJjb2x1bW4iLCJtYXRjaCIsImNhbk9wZW5JbkVkaXRvciIsIk51bWJlciIsInVuZGVmaW5lZCIsImNvbXBvbmVudFN0YWNrIiwiY29tcG9uZW50U3RhY2tGcmFtZXMiLCJsaW5lIiwidHJpbSIsInNwbGl0IiwiZXhlYyIsImNvbXBvbmVudCIsInB1c2giLCJpbmNsdWRlcyIsImZyYW1lTG9jYXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parse-component-stack.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parseStack.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parseStack.js ***!
  \***************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"parseStack\", ({\n    enumerable: true,\n    get: function() {\n        return parseStack;\n    }\n}));\nconst _stacktraceparser = __webpack_require__(/*! next/dist/compiled/stacktrace-parser */ \"(app-pages-browser)/./node_modules/next/dist/compiled/stacktrace-parser/stack-trace-parser.cjs.js\");\nconst regexNextStatic = /\\/_next(\\/static\\/.+)/;\nfunction parseStack(stack) {\n    const frames = (0, _stacktraceparser.parse)(stack);\n    return frames.map((frame)=>{\n        try {\n            const url = new URL(frame.file);\n            const res = regexNextStatic.exec(url.pathname);\n            if (res) {\n                var _process_env___NEXT_DIST_DIR_replace, _process_env___NEXT_DIST_DIR;\n                const distDir = (_process_env___NEXT_DIST_DIR = \"E:\\\\ankkorwoo\\\\ankkor\\\\.next\") == null ? void 0 : (_process_env___NEXT_DIST_DIR_replace = _process_env___NEXT_DIST_DIR.replace(/\\\\/g, \"/\")) == null ? void 0 : _process_env___NEXT_DIST_DIR_replace.replace(/\\/$/, \"\");\n                if (distDir) {\n                    frame.file = \"file://\" + distDir.concat(res.pop()) + url.search;\n                }\n            }\n        } catch (e) {}\n        return frame;\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=parseStack.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9wYXJzZVN0YWNrLmpzIiwibWFwcGluZ3MiOiI7Ozs7OENBS2dCQTs7O2VBQUFBOzs7OENBTE07QUFHdEIsTUFBTUMsa0JBQWtCO0FBRWpCLFNBQVNELFdBQVdFLEtBQWE7SUFDdEMsTUFBTUMsU0FBU0MsQ0FBQUEsR0FBQUEsa0JBQUFBLEtBQUssRUFBQ0Y7SUFDckIsT0FBT0MsT0FBT0UsR0FBRyxDQUFDLENBQUNDO1FBQ2pCLElBQUk7WUFDRixNQUFNQyxNQUFNLElBQUlDLElBQUlGLE1BQU1HLElBQUk7WUFDOUIsTUFBTUMsTUFBTVQsZ0JBQWdCVSxJQUFJLENBQUNKLElBQUlLLFFBQVE7WUFDN0MsSUFBSUYsS0FBSztvQkFDU0csc0NBQUFBO2dCQUFoQixNQUFNQyxVQUFBQSxDQUFVRCwrQkFBQUEsOEJBQTJCLHNCQUEzQkEsdUNBQUFBLDZCQUNaSSxPQUFPLENBQUMsT0FBTyx5QkFESEoscUNBRVpJLE9BQU8sQ0FBQyxPQUFPO2dCQUNuQixJQUFJSCxTQUFTO29CQUNYUixNQUFNRyxJQUFJLEdBQUcsWUFBWUssUUFBUUksTUFBTSxDQUFDUixJQUFJUyxHQUFHLE1BQU9aLElBQUlhLE1BQU07Z0JBQ2xFO1lBQ0Y7UUFDRixFQUFFLE9BQUFDLEdBQU0sQ0FBQztRQUNULE9BQU9mO0lBQ1Q7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hlbHBlcnMvcGFyc2VTdGFjay50cz83NzI5Il0sIm5hbWVzIjpbInBhcnNlU3RhY2siLCJyZWdleE5leHRTdGF0aWMiLCJzdGFjayIsImZyYW1lcyIsInBhcnNlIiwibWFwIiwiZnJhbWUiLCJ1cmwiLCJVUkwiLCJmaWxlIiwicmVzIiwiZXhlYyIsInBhdGhuYW1lIiwicHJvY2VzcyIsImRpc3REaXIiLCJlbnYiLCJfX05FWFRfRElTVF9ESVIiLCJyZXBsYWNlIiwiY29uY2F0IiwicG9wIiwic2VhcmNoIiwiZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parseStack.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/runtime-error-handler.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/runtime-error-handler.js ***!
  \**************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RuntimeErrorHandler\", ({\n    enumerable: true,\n    get: function() {\n        return RuntimeErrorHandler;\n    }\n}));\nconst RuntimeErrorHandler = {\n    hadRuntimeError: false\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=runtime-error-handler.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9ydW50aW1lLWVycm9yLWhhbmRsZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozt1REFBYUE7OztlQUFBQTs7O0FBQU4sTUFBTUEsc0JBQXNCO0lBQ2pDQyxpQkFBaUI7QUFDbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9oZWxwZXJzL3J1bnRpbWUtZXJyb3ItaGFuZGxlci50cz8xZDAzIl0sIm5hbWVzIjpbIlJ1bnRpbWVFcnJvckhhbmRsZXIiLCJoYWRSdW50aW1lRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/runtime-error-handler.js\n"));

/***/ })

}]);