/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_compiled_pl"],{

/***/ "./node_modules/next/dist/compiled/platform/platform.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/compiled/platform/platform.js ***!
  \**************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{var e={541:function(e,i,t){e=t.nmd(e);\n/*!\n * Platform.js v1.3.6\n * Copyright 2014-2020 Benjamin Tan\n * Copyright 2011-2013 John-David Dalton\n * Available under MIT license\n */(function(){\"use strict\";var t={function:true,object:true};var r=t[typeof window]&&window||this;var a=r;var n=t[typeof i]&&i;var o=t[\"object\"]&&e&&!e.nodeType&&e;var l=n&&o&&typeof __webpack_require__.g==\"object\"&&__webpack_require__.g;if(l&&(l.global===l||l.window===l||l.self===l)){r=l}var s=Math.pow(2,53)-1;var f=/\\bOpera/;var b=this;var c=Object.prototype;var p=c.hasOwnProperty;var u=c.toString;function capitalize(e){e=String(e);return e.charAt(0).toUpperCase()+e.slice(1)}function cleanupOS(e,i,t){var r={\"10.0\":\"10\",6.4:\"10 Technical Preview\",6.3:\"8.1\",6.2:\"8\",6.1:\"Server 2008 R2 / 7\",\"6.0\":\"Server 2008 / Vista\",5.2:\"Server 2003 / XP 64-bit\",5.1:\"XP\",5.01:\"2000 SP1\",\"5.0\":\"2000\",\"4.0\":\"NT\",\"4.90\":\"ME\"};if(i&&t&&/^Win/i.test(e)&&!/^Windows Phone /i.test(e)&&(r=r[/[\\d.]+$/.exec(e)])){e=\"Windows \"+r}e=String(e);if(i&&t){e=e.replace(RegExp(i,\"i\"),t)}e=format(e.replace(/ ce$/i,\" CE\").replace(/\\bhpw/i,\"web\").replace(/\\bMacintosh\\b/,\"Mac OS\").replace(/_PowerPC\\b/i,\" OS\").replace(/\\b(OS X) [^ \\d]+/i,\"$1\").replace(/\\bMac (OS X)\\b/,\"$1\").replace(/\\/(\\d)/,\" $1\").replace(/_/g,\".\").replace(/(?: BePC|[ .]*fc[ \\d.]+)$/i,\"\").replace(/\\bx86\\.64\\b/gi,\"x86_64\").replace(/\\b(Windows Phone) OS\\b/,\"$1\").replace(/\\b(Chrome OS \\w+) [\\d.]+\\b/,\"$1\").split(\" on \")[0]);return e}function each(e,i){var t=-1,r=e?e.length:0;if(typeof r==\"number\"&&r>-1&&r<=s){while(++t<r){i(e[t],t,e)}}else{forOwn(e,i)}}function format(e){e=trim(e);return/^(?:webOS|i(?:OS|P))/.test(e)?e:capitalize(e)}function forOwn(e,i){for(var t in e){if(p.call(e,t)){i(e[t],t,e)}}}function getClassOf(e){return e==null?capitalize(e):u.call(e).slice(8,-1)}function isHostType(e,i){var t=e!=null?typeof e[i]:\"number\";return!/^(?:boolean|number|string|undefined)$/.test(t)&&(t==\"object\"?!!e[i]:true)}function qualify(e){return String(e).replace(/([ -])(?!$)/g,\"$1?\")}function reduce(e,i){var t=null;each(e,(function(r,a){t=i(t,r,a,e)}));return t}function trim(e){return String(e).replace(/^ +| +$/g,\"\")}function parse(e){var i=r;var t=e&&typeof e==\"object\"&&getClassOf(e)!=\"String\";if(t){i=e;e=null}var n=i.navigator||{};var o=n.userAgent||\"\";e||(e=o);var l=t||b==a;var s=t?!!n.likeChrome:/\\bChrome\\b/.test(e)&&!/internal|\\n/i.test(u.toString());var c=\"Object\",p=t?c:\"ScriptBridgingProxyObject\",d=t?c:\"Environment\",S=t&&i.java?\"JavaPackage\":getClassOf(i.java),x=t?c:\"RuntimeObject\";var m=/\\bJava/.test(S)&&i.java;var g=m&&getClassOf(i.environment)==d;var h=m?\"a\":\"α\";var v=m?\"b\":\"β\";var O=i.document||{};var y=i.operamini||i.opera;var w=f.test(w=t&&y?y[\"[[Class]]\"]:getClassOf(y))?w:y=null;var M;var E=e;var P=[];var C=null;var k=e==o;var B=k&&y&&typeof y.version==\"function\"&&y.version();var W;var _=getLayout([{label:\"EdgeHTML\",pattern:\"Edge\"},\"Trident\",{label:\"WebKit\",pattern:\"AppleWebKit\"},\"iCab\",\"Presto\",\"NetFront\",\"Tasman\",\"KHTML\",\"Gecko\"]);var R=getName([\"Adobe AIR\",\"Arora\",\"Avant Browser\",\"Breach\",\"Camino\",\"Electron\",\"Epiphany\",\"Fennec\",\"Flock\",\"Galeon\",\"GreenBrowser\",\"iCab\",\"Iceweasel\",\"K-Meleon\",\"Konqueror\",\"Lunascape\",\"Maxthon\",{label:\"Microsoft Edge\",pattern:\"(?:Edge|Edg|EdgA|EdgiOS)\"},\"Midori\",\"Nook Browser\",\"PaleMoon\",\"PhantomJS\",\"Raven\",\"Rekonq\",\"RockMelt\",{label:\"Samsung Internet\",pattern:\"SamsungBrowser\"},\"SeaMonkey\",{label:\"Silk\",pattern:\"(?:Cloud9|Silk-Accelerated)\"},\"Sleipnir\",\"SlimBrowser\",{label:\"SRWare Iron\",pattern:\"Iron\"},\"Sunrise\",\"Swiftfox\",\"Vivaldi\",\"Waterfox\",\"WebPositive\",{label:\"Yandex Browser\",pattern:\"YaBrowser\"},{label:\"UC Browser\",pattern:\"UCBrowser\"},\"Opera Mini\",{label:\"Opera Mini\",pattern:\"OPiOS\"},\"Opera\",{label:\"Opera\",pattern:\"OPR\"},\"Chromium\",\"Chrome\",{label:\"Chrome\",pattern:\"(?:HeadlessChrome)\"},{label:\"Chrome Mobile\",pattern:\"(?:CriOS|CrMo)\"},{label:\"Firefox\",pattern:\"(?:Firefox|Minefield)\"},{label:\"Firefox for iOS\",pattern:\"FxiOS\"},{label:\"IE\",pattern:\"IEMobile\"},{label:\"IE\",pattern:\"MSIE\"},\"Safari\"]);var A=getProduct([{label:\"BlackBerry\",pattern:\"BB10\"},\"BlackBerry\",{label:\"Galaxy S\",pattern:\"GT-I9000\"},{label:\"Galaxy S2\",pattern:\"GT-I9100\"},{label:\"Galaxy S3\",pattern:\"GT-I9300\"},{label:\"Galaxy S4\",pattern:\"GT-I9500\"},{label:\"Galaxy S5\",pattern:\"SM-G900\"},{label:\"Galaxy S6\",pattern:\"SM-G920\"},{label:\"Galaxy S6 Edge\",pattern:\"SM-G925\"},{label:\"Galaxy S7\",pattern:\"SM-G930\"},{label:\"Galaxy S7 Edge\",pattern:\"SM-G935\"},\"Google TV\",\"Lumia\",\"iPad\",\"iPod\",\"iPhone\",\"Kindle\",{label:\"Kindle Fire\",pattern:\"(?:Cloud9|Silk-Accelerated)\"},\"Nexus\",\"Nook\",\"PlayBook\",\"PlayStation Vita\",\"PlayStation\",\"TouchPad\",\"Transformer\",{label:\"Wii U\",pattern:\"WiiU\"},\"Wii\",\"Xbox One\",{label:\"Xbox 360\",pattern:\"Xbox\"},\"Xoom\"]);var I=getManufacturer({Apple:{iPad:1,iPhone:1,iPod:1},Alcatel:{},Archos:{},Amazon:{Kindle:1,\"Kindle Fire\":1},Asus:{Transformer:1},\"Barnes & Noble\":{Nook:1},BlackBerry:{PlayBook:1},Google:{\"Google TV\":1,Nexus:1},HP:{TouchPad:1},HTC:{},Huawei:{},Lenovo:{},LG:{},Microsoft:{Xbox:1,\"Xbox One\":1},Motorola:{Xoom:1},Nintendo:{\"Wii U\":1,Wii:1},Nokia:{Lumia:1},Oppo:{},Samsung:{\"Galaxy S\":1,\"Galaxy S2\":1,\"Galaxy S3\":1,\"Galaxy S4\":1},Sony:{PlayStation:1,\"PlayStation Vita\":1},Xiaomi:{Mi:1,Redmi:1}});var T=getOS([\"Windows Phone\",\"KaiOS\",\"Android\",\"CentOS\",{label:\"Chrome OS\",pattern:\"CrOS\"},\"Debian\",{label:\"DragonFly BSD\",pattern:\"DragonFly\"},\"Fedora\",\"FreeBSD\",\"Gentoo\",\"Haiku\",\"Kubuntu\",\"Linux Mint\",\"OpenBSD\",\"Red Hat\",\"SuSE\",\"Ubuntu\",\"Xubuntu\",\"Cygwin\",\"Symbian OS\",\"hpwOS\",\"webOS \",\"webOS\",\"Tablet OS\",\"Tizen\",\"Linux\",\"Mac OS X\",\"Macintosh\",\"Mac\",\"Windows 98;\",\"Windows \"]);function getLayout(i){return reduce(i,(function(i,t){return i||RegExp(\"\\\\b\"+(t.pattern||qualify(t))+\"\\\\b\",\"i\").exec(e)&&(t.label||t)}))}function getManufacturer(i){return reduce(i,(function(i,t,r){return i||(t[A]||t[/^[a-z]+(?: +[a-z]+\\b)*/i.exec(A)]||RegExp(\"\\\\b\"+qualify(r)+\"(?:\\\\b|\\\\w*\\\\d)\",\"i\").exec(e))&&r}))}function getName(i){return reduce(i,(function(i,t){return i||RegExp(\"\\\\b\"+(t.pattern||qualify(t))+\"\\\\b\",\"i\").exec(e)&&(t.label||t)}))}function getOS(i){return reduce(i,(function(i,t){var r=t.pattern||qualify(t);if(!i&&(i=RegExp(\"\\\\b\"+r+\"(?:/[\\\\d.]+|[ \\\\w.]*)\",\"i\").exec(e))){i=cleanupOS(i,r,t.label||t)}return i}))}function getProduct(i){return reduce(i,(function(i,t){var r=t.pattern||qualify(t);if(!i&&(i=RegExp(\"\\\\b\"+r+\" *\\\\d+[.\\\\w_]*\",\"i\").exec(e)||RegExp(\"\\\\b\"+r+\" *\\\\w+-[\\\\w]*\",\"i\").exec(e)||RegExp(\"\\\\b\"+r+\"(?:; *(?:[a-z]+[_-])?[a-z]+\\\\d+|[^ ();-]*)\",\"i\").exec(e))){if((i=String(t.label&&!RegExp(r,\"i\").test(t.label)?t.label:i).split(\"/\"))[1]&&!/[\\d.]+/.test(i[0])){i[0]+=\" \"+i[1]}t=t.label||t;i=format(i[0].replace(RegExp(r,\"i\"),t).replace(RegExp(\"; *(?:\"+t+\"[_-])?\",\"i\"),\" \").replace(RegExp(\"(\"+t+\")[-_.]?(\\\\w)\",\"i\"),\"$1 $2\"))}return i}))}function getVersion(i){return reduce(i,(function(i,t){return i||(RegExp(t+\"(?:-[\\\\d.]+/|(?: for [\\\\w-]+)?[ /-])([\\\\d.]+[^ ();/_-]*)\",\"i\").exec(e)||0)[1]||null}))}function toStringPlatform(){return this.description||\"\"}_&&(_=[_]);if(/\\bAndroid\\b/.test(T)&&!A&&(M=/\\bAndroid[^;]*;(.*?)(?:Build|\\) AppleWebKit)\\b/i.exec(e))){A=trim(M[1]).replace(/^[a-z]{2}-[a-z]{2};\\s*/i,\"\")||null}if(I&&!A){A=getProduct([I])}else if(I&&A){A=A.replace(RegExp(\"^(\"+qualify(I)+\")[-_.\\\\s]\",\"i\"),I+\" \").replace(RegExp(\"^(\"+qualify(I)+\")[-_.]?(\\\\w)\",\"i\"),I+\" $2\")}if(M=/\\bGoogle TV\\b/.exec(A)){A=M[0]}if(/\\bSimulator\\b/i.test(e)){A=(A?A+\" \":\"\")+\"Simulator\"}if(R==\"Opera Mini\"&&/\\bOPiOS\\b/.test(e)){P.push(\"running in Turbo/Uncompressed mode\")}if(R==\"IE\"&&/\\blike iPhone OS\\b/.test(e)){M=parse(e.replace(/like iPhone OS/,\"\"));I=M.manufacturer;A=M.product}else if(/^iP/.test(A)){R||(R=\"Safari\");T=\"iOS\"+((M=/ OS ([\\d_]+)/i.exec(e))?\" \"+M[1].replace(/_/g,\".\"):\"\")}else if(R==\"Konqueror\"&&/^Linux\\b/i.test(T)){T=\"Kubuntu\"}else if(I&&I!=\"Google\"&&(/Chrome/.test(R)&&!/\\bMobile Safari\\b/i.test(e)||/\\bVita\\b/.test(A))||/\\bAndroid\\b/.test(T)&&/^Chrome/.test(R)&&/\\bVersion\\//i.test(e)){R=\"Android Browser\";T=/\\bAndroid\\b/.test(T)?T:\"Android\"}else if(R==\"Silk\"){if(!/\\bMobi/i.test(e)){T=\"Android\";P.unshift(\"desktop mode\")}if(/Accelerated *= *true/i.test(e)){P.unshift(\"accelerated\")}}else if(R==\"UC Browser\"&&/\\bUCWEB\\b/.test(e)){P.push(\"speed mode\")}else if(R==\"PaleMoon\"&&(M=/\\bFirefox\\/([\\d.]+)\\b/.exec(e))){P.push(\"identifying as Firefox \"+M[1])}else if(R==\"Firefox\"&&(M=/\\b(Mobile|Tablet|TV)\\b/i.exec(e))){T||(T=\"Firefox OS\");A||(A=M[1])}else if(!R||(M=!/\\bMinefield\\b/i.test(e)&&/\\b(?:Firefox|Safari)\\b/.exec(R))){if(R&&!A&&/[\\/,]|^[^(]+?\\)/.test(e.slice(e.indexOf(M+\"/\")+8))){R=null}if((M=A||I||T)&&(A||I||/\\b(?:Android|Symbian OS|Tablet OS|webOS)\\b/.test(T))){R=/[a-z]+(?: Hat)?/i.exec(/\\bAndroid\\b/.test(T)?T:M)+\" Browser\"}}else if(R==\"Electron\"&&(M=(/\\bChrome\\/([\\d.]+)\\b/.exec(e)||0)[1])){P.push(\"Chromium \"+M)}if(!B){B=getVersion([\"(?:Cloud9|CriOS|CrMo|Edge|Edg|EdgA|EdgiOS|FxiOS|HeadlessChrome|IEMobile|Iron|Opera ?Mini|OPiOS|OPR|Raven|SamsungBrowser|Silk(?!/[\\\\d.]+$)|UCBrowser|YaBrowser)\",\"Version\",qualify(R),\"(?:Firefox|Minefield|NetFront)\"])}if(M=_==\"iCab\"&&parseFloat(B)>3&&\"WebKit\"||/\\bOpera\\b/.test(R)&&(/\\bOPR\\b/.test(e)?\"Blink\":\"Presto\")||/\\b(?:Midori|Nook|Safari)\\b/i.test(e)&&!/^(?:Trident|EdgeHTML)$/.test(_)&&\"WebKit\"||!_&&/\\bMSIE\\b/i.test(e)&&(T==\"Mac OS\"?\"Tasman\":\"Trident\")||_==\"WebKit\"&&/\\bPlayStation\\b(?! Vita\\b)/i.test(R)&&\"NetFront\"){_=[M]}if(R==\"IE\"&&(M=(/; *(?:XBLWP|ZuneWP)(\\d+)/i.exec(e)||0)[1])){R+=\" Mobile\";T=\"Windows Phone \"+(/\\+$/.test(M)?M:M+\".x\");P.unshift(\"desktop mode\")}else if(/\\bWPDesktop\\b/i.test(e)){R=\"IE Mobile\";T=\"Windows Phone 8.x\";P.unshift(\"desktop mode\");B||(B=(/\\brv:([\\d.]+)/.exec(e)||0)[1])}else if(R!=\"IE\"&&_==\"Trident\"&&(M=/\\brv:([\\d.]+)/.exec(e))){if(R){P.push(\"identifying as \"+R+(B?\" \"+B:\"\"))}R=\"IE\";B=M[1]}if(k){if(isHostType(i,\"global\")){if(m){M=m.lang.System;E=M.getProperty(\"os.arch\");T=T||M.getProperty(\"os.name\")+\" \"+M.getProperty(\"os.version\")}if(g){try{B=i.require(\"ringo/engine\").version.join(\".\");R=\"RingoJS\"}catch(e){if((M=i.system)&&M.global.system==i.system){R=\"Narwhal\";T||(T=M[0].os||null)}}if(!R){R=\"Rhino\"}}else if(typeof i.process==\"object\"&&!i.process.browser&&(M=i.process)){if(typeof M.versions==\"object\"){if(typeof M.versions.electron==\"string\"){P.push(\"Node \"+M.versions.node);R=\"Electron\";B=M.versions.electron}else if(typeof M.versions.nw==\"string\"){P.push(\"Chromium \"+B,\"Node \"+M.versions.node);R=\"NW.js\";B=M.versions.nw}}if(!R){R=\"Node.js\";E=M.arch;T=M.platform;B=/[\\d.]+/.exec(M.version);B=B?B[0]:null}}}else if(getClassOf(M=i.runtime)==p){R=\"Adobe AIR\";T=M.flash.system.Capabilities.os}else if(getClassOf(M=i.phantom)==x){R=\"PhantomJS\";B=(M=M.version||null)&&M.major+\".\"+M.minor+\".\"+M.patch}else if(typeof O.documentMode==\"number\"&&(M=/\\bTrident\\/(\\d+)/i.exec(e))){B=[B,O.documentMode];if((M=+M[1]+4)!=B[1]){P.push(\"IE \"+B[1]+\" mode\");_&&(_[1]=\"\");B[1]=M}B=R==\"IE\"?String(B[1].toFixed(1)):B[0]}else if(typeof O.documentMode==\"number\"&&/^(?:Chrome|Firefox)\\b/.test(R)){P.push(\"masking as \"+R+\" \"+B);R=\"IE\";B=\"11.0\";_=[\"Trident\"];T=\"Windows\"}T=T&&format(T)}if(B&&(M=/(?:[ab]|dp|pre|[ab]\\d+pre)(?:\\d+\\+?)?$/i.exec(B)||/(?:alpha|beta)(?: ?\\d)?/i.exec(e+\";\"+(k&&n.appMinorVersion))||/\\bMinefield\\b/i.test(e)&&\"a\")){C=/b/i.test(M)?\"beta\":\"alpha\";B=B.replace(RegExp(M+\"\\\\+?$\"),\"\")+(C==\"beta\"?v:h)+(/\\d+\\+?/.exec(M)||\"\")}if(R==\"Fennec\"||R==\"Firefox\"&&/\\b(?:Android|Firefox OS|KaiOS)\\b/.test(T)){R=\"Firefox Mobile\"}else if(R==\"Maxthon\"&&B){B=B.replace(/\\.[\\d.]+/,\".x\")}else if(/\\bXbox\\b/i.test(A)){if(A==\"Xbox 360\"){T=null}if(A==\"Xbox 360\"&&/\\bIEMobile\\b/.test(e)){P.unshift(\"mobile mode\")}}else if((/^(?:Chrome|IE|Opera)$/.test(R)||R&&!A&&!/Browser|Mobi/.test(R))&&(T==\"Windows CE\"||/Mobi/i.test(e))){R+=\" Mobile\"}else if(R==\"IE\"&&k){try{if(i.external===null){P.unshift(\"platform preview\")}}catch(e){P.unshift(\"embedded\")}}else if((/\\bBlackBerry\\b/.test(A)||/\\bBB10\\b/.test(e))&&(M=(RegExp(A.replace(/ +/g,\" *\")+\"/([.\\\\d]+)\",\"i\").exec(e)||0)[1]||B)){M=[M,/BB10/.test(e)];T=(M[1]?(A=null,I=\"BlackBerry\"):\"Device Software\")+\" \"+M[0];B=null}else if(this!=forOwn&&A!=\"Wii\"&&(k&&y||/Opera/.test(R)&&/\\b(?:MSIE|Firefox)\\b/i.test(e)||R==\"Firefox\"&&/\\bOS X (?:\\d+\\.){2,}/.test(T)||R==\"IE\"&&(T&&!/^Win/.test(T)&&B>5.5||/\\bWindows XP\\b/.test(T)&&B>8||B==8&&!/\\bTrident\\b/.test(e)))&&!f.test(M=parse.call(forOwn,e.replace(f,\"\")+\";\"))&&M.name){M=\"ing as \"+M.name+((M=M.version)?\" \"+M:\"\");if(f.test(R)){if(/\\bIE\\b/.test(M)&&T==\"Mac OS\"){T=null}M=\"identify\"+M}else{M=\"mask\"+M;if(w){R=format(w.replace(/([a-z])([A-Z])/g,\"$1 $2\"))}else{R=\"Opera\"}if(/\\bIE\\b/.test(M)){T=null}if(!k){B=null}}_=[\"Presto\"];P.push(M)}if(M=(/\\bAppleWebKit\\/([\\d.]+\\+?)/i.exec(e)||0)[1]){M=[parseFloat(M.replace(/\\.(\\d)$/,\".0$1\")),M];if(R==\"Safari\"&&M[1].slice(-1)==\"+\"){R=\"WebKit Nightly\";C=\"alpha\";B=M[1].slice(0,-1)}else if(B==M[1]||B==(M[2]=(/\\bSafari\\/([\\d.]+\\+?)/i.exec(e)||0)[1])){B=null}M[1]=(/\\b(?:Headless)?Chrome\\/([\\d.]+)/i.exec(e)||0)[1];if(M[0]==537.36&&M[2]==537.36&&parseFloat(M[1])>=28&&_==\"WebKit\"){_=[\"Blink\"]}if(!k||!s&&!M[1]){_&&(_[1]=\"like Safari\");M=(M=M[0],M<400?1:M<500?2:M<526?3:M<533?4:M<534?\"4+\":M<535?5:M<537?6:M<538?7:M<601?8:M<602?9:M<604?10:M<606?11:M<608?12:\"12\")}else{_&&(_[1]=\"like Chrome\");M=M[1]||(M=M[0],M<530?1:M<532?2:M<532.05?3:M<533?4:M<534.03?5:M<534.07?6:M<534.1?7:M<534.13?8:M<534.16?9:M<534.24?10:M<534.3?11:M<535.01?12:M<535.02?\"13+\":M<535.07?15:M<535.11?16:M<535.19?17:M<536.05?18:M<536.1?19:M<537.01?20:M<537.11?\"21+\":M<537.13?23:M<537.18?24:M<537.24?25:M<537.36?26:_!=\"Blink\"?\"27\":\"28\")}_&&(_[1]+=\" \"+(M+=typeof M==\"number\"?\".x\":/[.+]/.test(M)?\"\":\"+\"));if(R==\"Safari\"&&(!B||parseInt(B)>45)){B=M}else if(R==\"Chrome\"&&/\\bHeadlessChrome/i.test(e)){P.unshift(\"headless\")}}if(R==\"Opera\"&&(M=/\\bzbov|zvav$/.exec(T))){R+=\" \";P.unshift(\"desktop mode\");if(M==\"zvav\"){R+=\"Mini\";B=null}else{R+=\"Mobile\"}T=T.replace(RegExp(\" *\"+M+\"$\"),\"\")}else if(R==\"Safari\"&&/\\bChrome\\b/.exec(_&&_[1])){P.unshift(\"desktop mode\");R=\"Chrome Mobile\";B=null;if(/\\bOS X\\b/.test(T)){I=\"Apple\";T=\"iOS 4.3+\"}else{T=null}}else if(/\\bSRWare Iron\\b/.test(R)&&!B){B=getVersion(\"Chrome\")}if(B&&B.indexOf(M=/[\\d.]+$/.exec(T))==0&&e.indexOf(\"/\"+M+\"-\")>-1){T=trim(T.replace(M,\"\"))}if(T&&T.indexOf(R)!=-1&&!RegExp(R+\" OS\").test(T)){T=T.replace(RegExp(\" *\"+qualify(R)+\" *\"),\"\")}if(_&&!/\\b(?:Avant|Nook)\\b/.test(R)&&(/Browser|Lunascape|Maxthon/.test(R)||R!=\"Safari\"&&/^iOS/.test(T)&&/\\bSafari\\b/.test(_[1])||/^(?:Adobe|Arora|Breach|Midori|Opera|Phantom|Rekonq|Rock|Samsung Internet|Sleipnir|SRWare Iron|Vivaldi|Web)/.test(R)&&_[1])){(M=_[_.length-1])&&P.push(M)}if(P.length){P=[\"(\"+P.join(\"; \")+\")\"]}if(I&&A&&A.indexOf(I)<0){P.push(\"on \"+I)}if(A){P.push((/^on /.test(P[P.length-1])?\"\":\"on \")+A)}if(T){M=/ ([\\d.+]+)$/.exec(T);W=M&&T.charAt(T.length-M[0].length-1)==\"/\";T={architecture:32,family:M&&!W?T.replace(M[0],\"\"):T,version:M?M[1]:null,toString:function(){var e=this.version;return this.family+(e&&!W?\" \"+e:\"\")+(this.architecture==64?\" 64-bit\":\"\")}}}if((M=/\\b(?:AMD|IA|Win|WOW|x86_|x)64\\b/i.exec(E))&&!/\\bi686\\b/i.test(E)){if(T){T.architecture=64;T.family=T.family.replace(RegExp(\" *\"+M),\"\")}if(R&&(/\\bWOW64\\b/i.test(e)||k&&/\\w(?:86|32)$/.test(n.cpuClass||n.platform)&&!/\\bWin64; x64\\b/i.test(e))){P.unshift(\"32-bit\")}}else if(T&&/^OS X/.test(T.family)&&R==\"Chrome\"&&parseFloat(B)>=39){T.architecture=64}e||(e=null);var F={};F.description=e;F.layout=_&&_[0];F.manufacturer=I;F.name=R;F.prerelease=C;F.product=A;F.ua=e;F.version=R&&B;F.os=T||{architecture:null,family:null,version:null,toString:function(){return\"null\"}};F.parse=parse;F.toString=toStringPlatform;if(F.version){P.unshift(B)}if(F.name){P.unshift(R)}if(T&&R&&!(T==String(T).split(\" \")[0]&&(T==R.split(\" \")[0]||A))){P.push(A?\"(\"+T+\")\":\"on \"+T)}if(P.length){F.description=P.join(\" \")}return F}var d=parse();if(n&&o){forOwn(d,(function(e,i){n[i]=e}))}else{r.platform=d}}).call(this)}};var i={};function __nccwpck_require__(t){var r=i[t];if(r!==undefined){return r.exports}var a=i[t]={id:t,loaded:false,exports:{}};var n=true;try{e[t].call(a.exports,a,a.exports,__nccwpck_require__);n=false}finally{if(n)delete i[t]}a.loaded=true;return a.exports}(()=>{__nccwpck_require__.nmd=e=>{e.paths=[];if(!e.children)e.children=[];return e}})();if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(541);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/compiled/platform/platform.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/platform/platform.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/compiled/platform/platform.js ***!
  \**************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{var e={541:function(e,i,t){e=t.nmd(e);\n/*!\n * Platform.js v1.3.6\n * Copyright 2014-2020 Benjamin Tan\n * Copyright 2011-2013 John-David Dalton\n * Available under MIT license\n */(function(){\"use strict\";var t={function:true,object:true};var r=t[typeof window]&&window||this;var a=r;var n=t[typeof i]&&i;var o=t[\"object\"]&&e&&!e.nodeType&&e;var l=n&&o&&typeof __webpack_require__.g==\"object\"&&__webpack_require__.g;if(l&&(l.global===l||l.window===l||l.self===l)){r=l}var s=Math.pow(2,53)-1;var f=/\\bOpera/;var b=this;var c=Object.prototype;var p=c.hasOwnProperty;var u=c.toString;function capitalize(e){e=String(e);return e.charAt(0).toUpperCase()+e.slice(1)}function cleanupOS(e,i,t){var r={\"10.0\":\"10\",6.4:\"10 Technical Preview\",6.3:\"8.1\",6.2:\"8\",6.1:\"Server 2008 R2 / 7\",\"6.0\":\"Server 2008 / Vista\",5.2:\"Server 2003 / XP 64-bit\",5.1:\"XP\",5.01:\"2000 SP1\",\"5.0\":\"2000\",\"4.0\":\"NT\",\"4.90\":\"ME\"};if(i&&t&&/^Win/i.test(e)&&!/^Windows Phone /i.test(e)&&(r=r[/[\\d.]+$/.exec(e)])){e=\"Windows \"+r}e=String(e);if(i&&t){e=e.replace(RegExp(i,\"i\"),t)}e=format(e.replace(/ ce$/i,\" CE\").replace(/\\bhpw/i,\"web\").replace(/\\bMacintosh\\b/,\"Mac OS\").replace(/_PowerPC\\b/i,\" OS\").replace(/\\b(OS X) [^ \\d]+/i,\"$1\").replace(/\\bMac (OS X)\\b/,\"$1\").replace(/\\/(\\d)/,\" $1\").replace(/_/g,\".\").replace(/(?: BePC|[ .]*fc[ \\d.]+)$/i,\"\").replace(/\\bx86\\.64\\b/gi,\"x86_64\").replace(/\\b(Windows Phone) OS\\b/,\"$1\").replace(/\\b(Chrome OS \\w+) [\\d.]+\\b/,\"$1\").split(\" on \")[0]);return e}function each(e,i){var t=-1,r=e?e.length:0;if(typeof r==\"number\"&&r>-1&&r<=s){while(++t<r){i(e[t],t,e)}}else{forOwn(e,i)}}function format(e){e=trim(e);return/^(?:webOS|i(?:OS|P))/.test(e)?e:capitalize(e)}function forOwn(e,i){for(var t in e){if(p.call(e,t)){i(e[t],t,e)}}}function getClassOf(e){return e==null?capitalize(e):u.call(e).slice(8,-1)}function isHostType(e,i){var t=e!=null?typeof e[i]:\"number\";return!/^(?:boolean|number|string|undefined)$/.test(t)&&(t==\"object\"?!!e[i]:true)}function qualify(e){return String(e).replace(/([ -])(?!$)/g,\"$1?\")}function reduce(e,i){var t=null;each(e,(function(r,a){t=i(t,r,a,e)}));return t}function trim(e){return String(e).replace(/^ +| +$/g,\"\")}function parse(e){var i=r;var t=e&&typeof e==\"object\"&&getClassOf(e)!=\"String\";if(t){i=e;e=null}var n=i.navigator||{};var o=n.userAgent||\"\";e||(e=o);var l=t||b==a;var s=t?!!n.likeChrome:/\\bChrome\\b/.test(e)&&!/internal|\\n/i.test(u.toString());var c=\"Object\",p=t?c:\"ScriptBridgingProxyObject\",d=t?c:\"Environment\",S=t&&i.java?\"JavaPackage\":getClassOf(i.java),x=t?c:\"RuntimeObject\";var m=/\\bJava/.test(S)&&i.java;var g=m&&getClassOf(i.environment)==d;var h=m?\"a\":\"α\";var v=m?\"b\":\"β\";var O=i.document||{};var y=i.operamini||i.opera;var w=f.test(w=t&&y?y[\"[[Class]]\"]:getClassOf(y))?w:y=null;var M;var E=e;var P=[];var C=null;var k=e==o;var B=k&&y&&typeof y.version==\"function\"&&y.version();var W;var _=getLayout([{label:\"EdgeHTML\",pattern:\"Edge\"},\"Trident\",{label:\"WebKit\",pattern:\"AppleWebKit\"},\"iCab\",\"Presto\",\"NetFront\",\"Tasman\",\"KHTML\",\"Gecko\"]);var R=getName([\"Adobe AIR\",\"Arora\",\"Avant Browser\",\"Breach\",\"Camino\",\"Electron\",\"Epiphany\",\"Fennec\",\"Flock\",\"Galeon\",\"GreenBrowser\",\"iCab\",\"Iceweasel\",\"K-Meleon\",\"Konqueror\",\"Lunascape\",\"Maxthon\",{label:\"Microsoft Edge\",pattern:\"(?:Edge|Edg|EdgA|EdgiOS)\"},\"Midori\",\"Nook Browser\",\"PaleMoon\",\"PhantomJS\",\"Raven\",\"Rekonq\",\"RockMelt\",{label:\"Samsung Internet\",pattern:\"SamsungBrowser\"},\"SeaMonkey\",{label:\"Silk\",pattern:\"(?:Cloud9|Silk-Accelerated)\"},\"Sleipnir\",\"SlimBrowser\",{label:\"SRWare Iron\",pattern:\"Iron\"},\"Sunrise\",\"Swiftfox\",\"Vivaldi\",\"Waterfox\",\"WebPositive\",{label:\"Yandex Browser\",pattern:\"YaBrowser\"},{label:\"UC Browser\",pattern:\"UCBrowser\"},\"Opera Mini\",{label:\"Opera Mini\",pattern:\"OPiOS\"},\"Opera\",{label:\"Opera\",pattern:\"OPR\"},\"Chromium\",\"Chrome\",{label:\"Chrome\",pattern:\"(?:HeadlessChrome)\"},{label:\"Chrome Mobile\",pattern:\"(?:CriOS|CrMo)\"},{label:\"Firefox\",pattern:\"(?:Firefox|Minefield)\"},{label:\"Firefox for iOS\",pattern:\"FxiOS\"},{label:\"IE\",pattern:\"IEMobile\"},{label:\"IE\",pattern:\"MSIE\"},\"Safari\"]);var A=getProduct([{label:\"BlackBerry\",pattern:\"BB10\"},\"BlackBerry\",{label:\"Galaxy S\",pattern:\"GT-I9000\"},{label:\"Galaxy S2\",pattern:\"GT-I9100\"},{label:\"Galaxy S3\",pattern:\"GT-I9300\"},{label:\"Galaxy S4\",pattern:\"GT-I9500\"},{label:\"Galaxy S5\",pattern:\"SM-G900\"},{label:\"Galaxy S6\",pattern:\"SM-G920\"},{label:\"Galaxy S6 Edge\",pattern:\"SM-G925\"},{label:\"Galaxy S7\",pattern:\"SM-G930\"},{label:\"Galaxy S7 Edge\",pattern:\"SM-G935\"},\"Google TV\",\"Lumia\",\"iPad\",\"iPod\",\"iPhone\",\"Kindle\",{label:\"Kindle Fire\",pattern:\"(?:Cloud9|Silk-Accelerated)\"},\"Nexus\",\"Nook\",\"PlayBook\",\"PlayStation Vita\",\"PlayStation\",\"TouchPad\",\"Transformer\",{label:\"Wii U\",pattern:\"WiiU\"},\"Wii\",\"Xbox One\",{label:\"Xbox 360\",pattern:\"Xbox\"},\"Xoom\"]);var I=getManufacturer({Apple:{iPad:1,iPhone:1,iPod:1},Alcatel:{},Archos:{},Amazon:{Kindle:1,\"Kindle Fire\":1},Asus:{Transformer:1},\"Barnes & Noble\":{Nook:1},BlackBerry:{PlayBook:1},Google:{\"Google TV\":1,Nexus:1},HP:{TouchPad:1},HTC:{},Huawei:{},Lenovo:{},LG:{},Microsoft:{Xbox:1,\"Xbox One\":1},Motorola:{Xoom:1},Nintendo:{\"Wii U\":1,Wii:1},Nokia:{Lumia:1},Oppo:{},Samsung:{\"Galaxy S\":1,\"Galaxy S2\":1,\"Galaxy S3\":1,\"Galaxy S4\":1},Sony:{PlayStation:1,\"PlayStation Vita\":1},Xiaomi:{Mi:1,Redmi:1}});var T=getOS([\"Windows Phone\",\"KaiOS\",\"Android\",\"CentOS\",{label:\"Chrome OS\",pattern:\"CrOS\"},\"Debian\",{label:\"DragonFly BSD\",pattern:\"DragonFly\"},\"Fedora\",\"FreeBSD\",\"Gentoo\",\"Haiku\",\"Kubuntu\",\"Linux Mint\",\"OpenBSD\",\"Red Hat\",\"SuSE\",\"Ubuntu\",\"Xubuntu\",\"Cygwin\",\"Symbian OS\",\"hpwOS\",\"webOS \",\"webOS\",\"Tablet OS\",\"Tizen\",\"Linux\",\"Mac OS X\",\"Macintosh\",\"Mac\",\"Windows 98;\",\"Windows \"]);function getLayout(i){return reduce(i,(function(i,t){return i||RegExp(\"\\\\b\"+(t.pattern||qualify(t))+\"\\\\b\",\"i\").exec(e)&&(t.label||t)}))}function getManufacturer(i){return reduce(i,(function(i,t,r){return i||(t[A]||t[/^[a-z]+(?: +[a-z]+\\b)*/i.exec(A)]||RegExp(\"\\\\b\"+qualify(r)+\"(?:\\\\b|\\\\w*\\\\d)\",\"i\").exec(e))&&r}))}function getName(i){return reduce(i,(function(i,t){return i||RegExp(\"\\\\b\"+(t.pattern||qualify(t))+\"\\\\b\",\"i\").exec(e)&&(t.label||t)}))}function getOS(i){return reduce(i,(function(i,t){var r=t.pattern||qualify(t);if(!i&&(i=RegExp(\"\\\\b\"+r+\"(?:/[\\\\d.]+|[ \\\\w.]*)\",\"i\").exec(e))){i=cleanupOS(i,r,t.label||t)}return i}))}function getProduct(i){return reduce(i,(function(i,t){var r=t.pattern||qualify(t);if(!i&&(i=RegExp(\"\\\\b\"+r+\" *\\\\d+[.\\\\w_]*\",\"i\").exec(e)||RegExp(\"\\\\b\"+r+\" *\\\\w+-[\\\\w]*\",\"i\").exec(e)||RegExp(\"\\\\b\"+r+\"(?:; *(?:[a-z]+[_-])?[a-z]+\\\\d+|[^ ();-]*)\",\"i\").exec(e))){if((i=String(t.label&&!RegExp(r,\"i\").test(t.label)?t.label:i).split(\"/\"))[1]&&!/[\\d.]+/.test(i[0])){i[0]+=\" \"+i[1]}t=t.label||t;i=format(i[0].replace(RegExp(r,\"i\"),t).replace(RegExp(\"; *(?:\"+t+\"[_-])?\",\"i\"),\" \").replace(RegExp(\"(\"+t+\")[-_.]?(\\\\w)\",\"i\"),\"$1 $2\"))}return i}))}function getVersion(i){return reduce(i,(function(i,t){return i||(RegExp(t+\"(?:-[\\\\d.]+/|(?: for [\\\\w-]+)?[ /-])([\\\\d.]+[^ ();/_-]*)\",\"i\").exec(e)||0)[1]||null}))}function toStringPlatform(){return this.description||\"\"}_&&(_=[_]);if(/\\bAndroid\\b/.test(T)&&!A&&(M=/\\bAndroid[^;]*;(.*?)(?:Build|\\) AppleWebKit)\\b/i.exec(e))){A=trim(M[1]).replace(/^[a-z]{2}-[a-z]{2};\\s*/i,\"\")||null}if(I&&!A){A=getProduct([I])}else if(I&&A){A=A.replace(RegExp(\"^(\"+qualify(I)+\")[-_.\\\\s]\",\"i\"),I+\" \").replace(RegExp(\"^(\"+qualify(I)+\")[-_.]?(\\\\w)\",\"i\"),I+\" $2\")}if(M=/\\bGoogle TV\\b/.exec(A)){A=M[0]}if(/\\bSimulator\\b/i.test(e)){A=(A?A+\" \":\"\")+\"Simulator\"}if(R==\"Opera Mini\"&&/\\bOPiOS\\b/.test(e)){P.push(\"running in Turbo/Uncompressed mode\")}if(R==\"IE\"&&/\\blike iPhone OS\\b/.test(e)){M=parse(e.replace(/like iPhone OS/,\"\"));I=M.manufacturer;A=M.product}else if(/^iP/.test(A)){R||(R=\"Safari\");T=\"iOS\"+((M=/ OS ([\\d_]+)/i.exec(e))?\" \"+M[1].replace(/_/g,\".\"):\"\")}else if(R==\"Konqueror\"&&/^Linux\\b/i.test(T)){T=\"Kubuntu\"}else if(I&&I!=\"Google\"&&(/Chrome/.test(R)&&!/\\bMobile Safari\\b/i.test(e)||/\\bVita\\b/.test(A))||/\\bAndroid\\b/.test(T)&&/^Chrome/.test(R)&&/\\bVersion\\//i.test(e)){R=\"Android Browser\";T=/\\bAndroid\\b/.test(T)?T:\"Android\"}else if(R==\"Silk\"){if(!/\\bMobi/i.test(e)){T=\"Android\";P.unshift(\"desktop mode\")}if(/Accelerated *= *true/i.test(e)){P.unshift(\"accelerated\")}}else if(R==\"UC Browser\"&&/\\bUCWEB\\b/.test(e)){P.push(\"speed mode\")}else if(R==\"PaleMoon\"&&(M=/\\bFirefox\\/([\\d.]+)\\b/.exec(e))){P.push(\"identifying as Firefox \"+M[1])}else if(R==\"Firefox\"&&(M=/\\b(Mobile|Tablet|TV)\\b/i.exec(e))){T||(T=\"Firefox OS\");A||(A=M[1])}else if(!R||(M=!/\\bMinefield\\b/i.test(e)&&/\\b(?:Firefox|Safari)\\b/.exec(R))){if(R&&!A&&/[\\/,]|^[^(]+?\\)/.test(e.slice(e.indexOf(M+\"/\")+8))){R=null}if((M=A||I||T)&&(A||I||/\\b(?:Android|Symbian OS|Tablet OS|webOS)\\b/.test(T))){R=/[a-z]+(?: Hat)?/i.exec(/\\bAndroid\\b/.test(T)?T:M)+\" Browser\"}}else if(R==\"Electron\"&&(M=(/\\bChrome\\/([\\d.]+)\\b/.exec(e)||0)[1])){P.push(\"Chromium \"+M)}if(!B){B=getVersion([\"(?:Cloud9|CriOS|CrMo|Edge|Edg|EdgA|EdgiOS|FxiOS|HeadlessChrome|IEMobile|Iron|Opera ?Mini|OPiOS|OPR|Raven|SamsungBrowser|Silk(?!/[\\\\d.]+$)|UCBrowser|YaBrowser)\",\"Version\",qualify(R),\"(?:Firefox|Minefield|NetFront)\"])}if(M=_==\"iCab\"&&parseFloat(B)>3&&\"WebKit\"||/\\bOpera\\b/.test(R)&&(/\\bOPR\\b/.test(e)?\"Blink\":\"Presto\")||/\\b(?:Midori|Nook|Safari)\\b/i.test(e)&&!/^(?:Trident|EdgeHTML)$/.test(_)&&\"WebKit\"||!_&&/\\bMSIE\\b/i.test(e)&&(T==\"Mac OS\"?\"Tasman\":\"Trident\")||_==\"WebKit\"&&/\\bPlayStation\\b(?! Vita\\b)/i.test(R)&&\"NetFront\"){_=[M]}if(R==\"IE\"&&(M=(/; *(?:XBLWP|ZuneWP)(\\d+)/i.exec(e)||0)[1])){R+=\" Mobile\";T=\"Windows Phone \"+(/\\+$/.test(M)?M:M+\".x\");P.unshift(\"desktop mode\")}else if(/\\bWPDesktop\\b/i.test(e)){R=\"IE Mobile\";T=\"Windows Phone 8.x\";P.unshift(\"desktop mode\");B||(B=(/\\brv:([\\d.]+)/.exec(e)||0)[1])}else if(R!=\"IE\"&&_==\"Trident\"&&(M=/\\brv:([\\d.]+)/.exec(e))){if(R){P.push(\"identifying as \"+R+(B?\" \"+B:\"\"))}R=\"IE\";B=M[1]}if(k){if(isHostType(i,\"global\")){if(m){M=m.lang.System;E=M.getProperty(\"os.arch\");T=T||M.getProperty(\"os.name\")+\" \"+M.getProperty(\"os.version\")}if(g){try{B=i.require(\"ringo/engine\").version.join(\".\");R=\"RingoJS\"}catch(e){if((M=i.system)&&M.global.system==i.system){R=\"Narwhal\";T||(T=M[0].os||null)}}if(!R){R=\"Rhino\"}}else if(typeof i.process==\"object\"&&!i.process.browser&&(M=i.process)){if(typeof M.versions==\"object\"){if(typeof M.versions.electron==\"string\"){P.push(\"Node \"+M.versions.node);R=\"Electron\";B=M.versions.electron}else if(typeof M.versions.nw==\"string\"){P.push(\"Chromium \"+B,\"Node \"+M.versions.node);R=\"NW.js\";B=M.versions.nw}}if(!R){R=\"Node.js\";E=M.arch;T=M.platform;B=/[\\d.]+/.exec(M.version);B=B?B[0]:null}}}else if(getClassOf(M=i.runtime)==p){R=\"Adobe AIR\";T=M.flash.system.Capabilities.os}else if(getClassOf(M=i.phantom)==x){R=\"PhantomJS\";B=(M=M.version||null)&&M.major+\".\"+M.minor+\".\"+M.patch}else if(typeof O.documentMode==\"number\"&&(M=/\\bTrident\\/(\\d+)/i.exec(e))){B=[B,O.documentMode];if((M=+M[1]+4)!=B[1]){P.push(\"IE \"+B[1]+\" mode\");_&&(_[1]=\"\");B[1]=M}B=R==\"IE\"?String(B[1].toFixed(1)):B[0]}else if(typeof O.documentMode==\"number\"&&/^(?:Chrome|Firefox)\\b/.test(R)){P.push(\"masking as \"+R+\" \"+B);R=\"IE\";B=\"11.0\";_=[\"Trident\"];T=\"Windows\"}T=T&&format(T)}if(B&&(M=/(?:[ab]|dp|pre|[ab]\\d+pre)(?:\\d+\\+?)?$/i.exec(B)||/(?:alpha|beta)(?: ?\\d)?/i.exec(e+\";\"+(k&&n.appMinorVersion))||/\\bMinefield\\b/i.test(e)&&\"a\")){C=/b/i.test(M)?\"beta\":\"alpha\";B=B.replace(RegExp(M+\"\\\\+?$\"),\"\")+(C==\"beta\"?v:h)+(/\\d+\\+?/.exec(M)||\"\")}if(R==\"Fennec\"||R==\"Firefox\"&&/\\b(?:Android|Firefox OS|KaiOS)\\b/.test(T)){R=\"Firefox Mobile\"}else if(R==\"Maxthon\"&&B){B=B.replace(/\\.[\\d.]+/,\".x\")}else if(/\\bXbox\\b/i.test(A)){if(A==\"Xbox 360\"){T=null}if(A==\"Xbox 360\"&&/\\bIEMobile\\b/.test(e)){P.unshift(\"mobile mode\")}}else if((/^(?:Chrome|IE|Opera)$/.test(R)||R&&!A&&!/Browser|Mobi/.test(R))&&(T==\"Windows CE\"||/Mobi/i.test(e))){R+=\" Mobile\"}else if(R==\"IE\"&&k){try{if(i.external===null){P.unshift(\"platform preview\")}}catch(e){P.unshift(\"embedded\")}}else if((/\\bBlackBerry\\b/.test(A)||/\\bBB10\\b/.test(e))&&(M=(RegExp(A.replace(/ +/g,\" *\")+\"/([.\\\\d]+)\",\"i\").exec(e)||0)[1]||B)){M=[M,/BB10/.test(e)];T=(M[1]?(A=null,I=\"BlackBerry\"):\"Device Software\")+\" \"+M[0];B=null}else if(this!=forOwn&&A!=\"Wii\"&&(k&&y||/Opera/.test(R)&&/\\b(?:MSIE|Firefox)\\b/i.test(e)||R==\"Firefox\"&&/\\bOS X (?:\\d+\\.){2,}/.test(T)||R==\"IE\"&&(T&&!/^Win/.test(T)&&B>5.5||/\\bWindows XP\\b/.test(T)&&B>8||B==8&&!/\\bTrident\\b/.test(e)))&&!f.test(M=parse.call(forOwn,e.replace(f,\"\")+\";\"))&&M.name){M=\"ing as \"+M.name+((M=M.version)?\" \"+M:\"\");if(f.test(R)){if(/\\bIE\\b/.test(M)&&T==\"Mac OS\"){T=null}M=\"identify\"+M}else{M=\"mask\"+M;if(w){R=format(w.replace(/([a-z])([A-Z])/g,\"$1 $2\"))}else{R=\"Opera\"}if(/\\bIE\\b/.test(M)){T=null}if(!k){B=null}}_=[\"Presto\"];P.push(M)}if(M=(/\\bAppleWebKit\\/([\\d.]+\\+?)/i.exec(e)||0)[1]){M=[parseFloat(M.replace(/\\.(\\d)$/,\".0$1\")),M];if(R==\"Safari\"&&M[1].slice(-1)==\"+\"){R=\"WebKit Nightly\";C=\"alpha\";B=M[1].slice(0,-1)}else if(B==M[1]||B==(M[2]=(/\\bSafari\\/([\\d.]+\\+?)/i.exec(e)||0)[1])){B=null}M[1]=(/\\b(?:Headless)?Chrome\\/([\\d.]+)/i.exec(e)||0)[1];if(M[0]==537.36&&M[2]==537.36&&parseFloat(M[1])>=28&&_==\"WebKit\"){_=[\"Blink\"]}if(!k||!s&&!M[1]){_&&(_[1]=\"like Safari\");M=(M=M[0],M<400?1:M<500?2:M<526?3:M<533?4:M<534?\"4+\":M<535?5:M<537?6:M<538?7:M<601?8:M<602?9:M<604?10:M<606?11:M<608?12:\"12\")}else{_&&(_[1]=\"like Chrome\");M=M[1]||(M=M[0],M<530?1:M<532?2:M<532.05?3:M<533?4:M<534.03?5:M<534.07?6:M<534.1?7:M<534.13?8:M<534.16?9:M<534.24?10:M<534.3?11:M<535.01?12:M<535.02?\"13+\":M<535.07?15:M<535.11?16:M<535.19?17:M<536.05?18:M<536.1?19:M<537.01?20:M<537.11?\"21+\":M<537.13?23:M<537.18?24:M<537.24?25:M<537.36?26:_!=\"Blink\"?\"27\":\"28\")}_&&(_[1]+=\" \"+(M+=typeof M==\"number\"?\".x\":/[.+]/.test(M)?\"\":\"+\"));if(R==\"Safari\"&&(!B||parseInt(B)>45)){B=M}else if(R==\"Chrome\"&&/\\bHeadlessChrome/i.test(e)){P.unshift(\"headless\")}}if(R==\"Opera\"&&(M=/\\bzbov|zvav$/.exec(T))){R+=\" \";P.unshift(\"desktop mode\");if(M==\"zvav\"){R+=\"Mini\";B=null}else{R+=\"Mobile\"}T=T.replace(RegExp(\" *\"+M+\"$\"),\"\")}else if(R==\"Safari\"&&/\\bChrome\\b/.exec(_&&_[1])){P.unshift(\"desktop mode\");R=\"Chrome Mobile\";B=null;if(/\\bOS X\\b/.test(T)){I=\"Apple\";T=\"iOS 4.3+\"}else{T=null}}else if(/\\bSRWare Iron\\b/.test(R)&&!B){B=getVersion(\"Chrome\")}if(B&&B.indexOf(M=/[\\d.]+$/.exec(T))==0&&e.indexOf(\"/\"+M+\"-\")>-1){T=trim(T.replace(M,\"\"))}if(T&&T.indexOf(R)!=-1&&!RegExp(R+\" OS\").test(T)){T=T.replace(RegExp(\" *\"+qualify(R)+\" *\"),\"\")}if(_&&!/\\b(?:Avant|Nook)\\b/.test(R)&&(/Browser|Lunascape|Maxthon/.test(R)||R!=\"Safari\"&&/^iOS/.test(T)&&/\\bSafari\\b/.test(_[1])||/^(?:Adobe|Arora|Breach|Midori|Opera|Phantom|Rekonq|Rock|Samsung Internet|Sleipnir|SRWare Iron|Vivaldi|Web)/.test(R)&&_[1])){(M=_[_.length-1])&&P.push(M)}if(P.length){P=[\"(\"+P.join(\"; \")+\")\"]}if(I&&A&&A.indexOf(I)<0){P.push(\"on \"+I)}if(A){P.push((/^on /.test(P[P.length-1])?\"\":\"on \")+A)}if(T){M=/ ([\\d.+]+)$/.exec(T);W=M&&T.charAt(T.length-M[0].length-1)==\"/\";T={architecture:32,family:M&&!W?T.replace(M[0],\"\"):T,version:M?M[1]:null,toString:function(){var e=this.version;return this.family+(e&&!W?\" \"+e:\"\")+(this.architecture==64?\" 64-bit\":\"\")}}}if((M=/\\b(?:AMD|IA|Win|WOW|x86_|x)64\\b/i.exec(E))&&!/\\bi686\\b/i.test(E)){if(T){T.architecture=64;T.family=T.family.replace(RegExp(\" *\"+M),\"\")}if(R&&(/\\bWOW64\\b/i.test(e)||k&&/\\w(?:86|32)$/.test(n.cpuClass||n.platform)&&!/\\bWin64; x64\\b/i.test(e))){P.unshift(\"32-bit\")}}else if(T&&/^OS X/.test(T.family)&&R==\"Chrome\"&&parseFloat(B)>=39){T.architecture=64}e||(e=null);var F={};F.description=e;F.layout=_&&_[0];F.manufacturer=I;F.name=R;F.prerelease=C;F.product=A;F.ua=e;F.version=R&&B;F.os=T||{architecture:null,family:null,version:null,toString:function(){return\"null\"}};F.parse=parse;F.toString=toStringPlatform;if(F.version){P.unshift(B)}if(F.name){P.unshift(R)}if(T&&R&&!(T==String(T).split(\" \")[0]&&(T==R.split(\" \")[0]||A))){P.push(A?\"(\"+T+\")\":\"on \"+T)}if(P.length){F.description=P.join(\" \")}return F}var d=parse();if(n&&o){forOwn(d,(function(e,i){n[i]=e}))}else{r.platform=d}}).call(this)}};var i={};function __nccwpck_require__(t){var r=i[t];if(r!==undefined){return r.exports}var a=i[t]={id:t,loaded:false,exports:{}};var n=true;try{e[t].call(a.exports,a,a.exports,__nccwpck_require__);n=false}finally{if(n)delete i[t]}a.loaded=true;return a.exports}(()=>{__nccwpck_require__.nmd=e=>{e.paths=[];if(!e.children)e.children=[];return e}})();if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(541);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/platform/platform.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/compiled/process/browser.js ***!
  \************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(function(){var e={229:function(e){var t=e.exports={};var r;var n;function defaultSetTimout(){throw new Error(\"setTimeout has not been defined\")}function defaultClearTimeout(){throw new Error(\"clearTimeout has not been defined\")}(function(){try{if(typeof setTimeout===\"function\"){r=setTimeout}else{r=defaultSetTimout}}catch(e){r=defaultSetTimout}try{if(typeof clearTimeout===\"function\"){n=clearTimeout}else{n=defaultClearTimeout}}catch(e){n=defaultClearTimeout}})();function runTimeout(e){if(r===setTimeout){return setTimeout(e,0)}if((r===defaultSetTimout||!r)&&setTimeout){r=setTimeout;return setTimeout(e,0)}try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}function runClearTimeout(e){if(n===clearTimeout){return clearTimeout(e)}if((n===defaultClearTimeout||!n)&&clearTimeout){n=clearTimeout;return clearTimeout(e)}try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}var i=[];var o=false;var u;var a=-1;function cleanUpNextTick(){if(!o||!u){return}o=false;if(u.length){i=u.concat(i)}else{a=-1}if(i.length){drainQueue()}}function drainQueue(){if(o){return}var e=runTimeout(cleanUpNextTick);o=true;var t=i.length;while(t){u=i;i=[];while(++a<t){if(u){u[a].run()}}a=-1;t=i.length}u=null;o=false;runClearTimeout(e)}t.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}i.push(new Item(e,t));if(i.length===1&&!o){runTimeout(drainQueue)}};function Item(e,t){this.fun=e;this.array=t}Item.prototype.run=function(){this.fun.apply(null,this.array)};t.title=\"browser\";t.browser=true;t.env={};t.argv=[];t.version=\"\";t.versions={};function noop(){}t.on=noop;t.addListener=noop;t.once=noop;t.off=noop;t.removeListener=noop;t.removeAllListeners=noop;t.emit=noop;t.prependListener=noop;t.prependOnceListener=noop;t.listeners=function(e){return[]};t.binding=function(e){throw new Error(\"process.binding is not supported\")};t.cwd=function(){return\"/\"};t.chdir=function(e){throw new Error(\"process.chdir is not supported\")};t.umask=function(){return 0}}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var i=t[r]={exports:{}};var o=true;try{e[r](i,i.exports,__nccwpck_require__);o=false}finally{if(o)delete t[r]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var r=__nccwpck_require__(229);module.exports=r})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\n"));

/***/ })

}]);