"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_RuntimeError-faf2cb51"],{

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.js ***!
  \**********************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CallStackFrame\", ({\n    enumerable: true,\n    get: function() {\n        return CallStackFrame;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _stackframe = __webpack_require__(/*! ../../helpers/stack-frame */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js\");\nconst _useopenineditor = __webpack_require__(/*! ../../helpers/use-open-in-editor */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../../components/hot-linked-text */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/components/hot-linked-text/index.js\");\nconst CallStackFrame = function CallStackFrame(param) {\n    let { frame } = param;\n    var _frame_originalStackFrame;\n    // TODO: ability to expand resolved frames\n    // TODO: render error or external indicator\n    const f = (_frame_originalStackFrame = frame.originalStackFrame) != null ? _frame_originalStackFrame : frame.sourceStackFrame;\n    const hasSource = Boolean(frame.originalCodeFrame);\n    const open = (0, _useopenineditor.useOpenInEditor)(hasSource ? {\n        file: f.file,\n        lineNumber: f.lineNumber,\n        column: f.column\n    } : undefined);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-call-stack-frame\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h3\", {\n                \"data-nextjs-frame-expanded\": Boolean(frame.expanded),\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                    text: f.methodName\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                \"data-has-source\": hasSource ? \"true\" : undefined,\n                tabIndex: hasSource ? 10 : undefined,\n                role: hasSource ? \"link\" : undefined,\n                onClick: open,\n                title: hasSource ? \"Click to open in your editor\" : undefined,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                        children: (0, _stackframe.getFrameSource)(f)\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                                d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"polyline\", {\n                                points: \"15 3 21 3 21 9\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"line\", {\n                                x1: \"10\",\n                                y1: \"14\",\n                                x2: \"21\",\n                                y2: \"3\"\n                            })\n                        ]\n                    })\n                ]\n            })\n        ]\n    });\n};\n_c = CallStackFrame;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=CallStackFrame.js.map\nvar _c;\n$RefreshReg$(_c, \"CallStackFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/GroupedStackFrames.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/GroupedStackFrames.js ***!
  \**************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"GroupedStackFrames\", ({\n    enumerable: true,\n    get: function() {\n        return GroupedStackFrames;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _CallStackFrame = __webpack_require__(/*! ./CallStackFrame */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.js\");\nconst _CollapseIcon = __webpack_require__(/*! ../../icons/CollapseIcon */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CollapseIcon.js\");\nconst _FrameworkIcon = __webpack_require__(/*! ../../icons/FrameworkIcon */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/FrameworkIcon.js\");\nfunction FrameworkGroup(param) {\n    let { framework, stackFrames } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"details\", {\n        \"data-nextjs-collapsed-call-stack-details\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"summary\", {\n                tabIndex: 10,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_CollapseIcon.CollapseIcon, {}),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_FrameworkIcon.FrameworkIcon, {\n                        framework: framework\n                    }),\n                    framework === \"react\" ? \"React\" : \"Next.js\"\n                ]\n            }),\n            stackFrames.map((frame, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_CallStackFrame.CallStackFrame, {\n                    frame: frame\n                }, \"call-stack-\" + index))\n        ]\n    });\n}\n_c = FrameworkGroup;\nfunction GroupedStackFrames(param) {\n    let { groupedStackFrames, show } = param;\n    if (!show) return;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: groupedStackFrames.map((stackFramesGroup, groupIndex)=>{\n            // Collapse React and Next.js frames\n            if (stackFramesGroup.framework) {\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(FrameworkGroup, {\n                    framework: stackFramesGroup.framework,\n                    stackFrames: stackFramesGroup.stackFrames\n                }, \"call-stack-framework-group-\" + groupIndex);\n            }\n            return stackFramesGroup.stackFrames.map((frame, frameIndex)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_CallStackFrame.CallStackFrame, {\n                    frame: frame\n                }, \"call-stack-\" + groupIndex + \"-\" + frameIndex));\n        })\n    });\n}\n_c1 = GroupedStackFrames;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=GroupedStackFrames.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"FrameworkGroup\");\n$RefreshReg$(_c1, \"GroupedStackFrames\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/GroupedStackFrames.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/component-stack-pseudo-html.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/component-stack-pseudo-html.js ***!
  \***********************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PseudoHtmlDiff\", ({\n    enumerable: true,\n    get: function() {\n        return PseudoHtmlDiff;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nconst _CollapseIcon = __webpack_require__(/*! ../../icons/CollapseIcon */ \"./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CollapseIcon.js\");\nfunction getAdjacentProps(isAdj) {\n    return {\n        \"data-nextjs-container-errors-pseudo-html--tag-adjacent\": isAdj\n    };\n}\nfunction PseudoHtmlDiff(param) {\n    let { componentStackFrames, firstContent, secondContent, hydrationMismatchType, ...props } = param;\n    const isHtmlTagsWarning = hydrationMismatchType === \"tag\";\n    // For text mismatch, mismatched text will take 2 rows, so we display 4 rows of component stack\n    const MAX_NON_COLLAPSED_FRAMES = isHtmlTagsWarning ? 6 : 4;\n    const shouldCollapse = componentStackFrames.length > MAX_NON_COLLAPSED_FRAMES;\n    const [isHtmlCollapsed, toggleCollapseHtml] = (0, _react.useState)(shouldCollapse);\n    const htmlComponents = (0, _react.useMemo)(()=>{\n        const tagNames = isHtmlTagsWarning ? [\n            firstContent.replace(/<|>/g, \"\"),\n            secondContent.replace(/<|>/g, \"\")\n        ] : [];\n        const nestedHtmlStack = [];\n        let lastText = \"\";\n        const componentStack = componentStackFrames.map((frame)=>frame.component).reverse();\n        // [child index, parent index]\n        const matchedIndex = [\n            -1,\n            -1\n        ];\n        if (isHtmlTagsWarning) {\n            // Reverse search for the child tag\n            for(let i = componentStack.length - 1; i >= 0; i--){\n                if (componentStack[i] === tagNames[0]) {\n                    matchedIndex[0] = i;\n                    break;\n                }\n            }\n            // Start searching parent tag from child tag above\n            for(let i = matchedIndex[0] - 1; i >= 0; i--){\n                if (componentStack[i] === tagNames[1]) {\n                    matchedIndex[1] = i;\n                    break;\n                }\n            }\n        }\n        componentStack.forEach((component, index, componentList)=>{\n            const spaces = \" \".repeat(nestedHtmlStack.length * 2);\n            // const prevComponent = componentList[index - 1]\n            // const nextComponent = componentList[index + 1]\n            // When component is the server or client tag name, highlight it\n            const isHighlightedTag = isHtmlTagsWarning ? index === matchedIndex[0] || index === matchedIndex[1] : tagNames.includes(component);\n            const isAdjacentTag = isHighlightedTag || Math.abs(index - matchedIndex[0]) <= 1 || Math.abs(index - matchedIndex[1]) <= 1;\n            const isLastFewFrames = !isHtmlTagsWarning && index >= componentList.length - 6;\n            const adjProps = getAdjacentProps(isAdjacentTag);\n            if (isHtmlTagsWarning && isAdjacentTag || isLastFewFrames) {\n                const codeLine = /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                    children: [\n                        spaces,\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            ...adjProps,\n                            ...isHighlightedTag ? {\n                                \"data-nextjs-container-errors-pseudo-html--tag-error\": true\n                            } : undefined,\n                            children: \"<\" + component + \">\\n\"\n                        })\n                    ]\n                });\n                lastText = component;\n                const wrappedCodeLine = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_react.Fragment, {\n                    children: [\n                        codeLine,\n                        isHighlightedTag && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html--hint\": true,\n                            children: spaces + \"^\".repeat(component.length + 2) + \"\\n\"\n                        })\n                    ]\n                }, nestedHtmlStack.length);\n                nestedHtmlStack.push(wrappedCodeLine);\n            } else {\n                if (nestedHtmlStack.length >= MAX_NON_COLLAPSED_FRAMES && isHtmlCollapsed) {\n                    return;\n                }\n                if (!isHtmlCollapsed || isLastFewFrames) {\n                    nestedHtmlStack.push(/*#__PURE__*/ (0, _react.createElement)(\"span\", {\n                        ...adjProps,\n                        key: nestedHtmlStack.length,\n                        children: [\n                            spaces,\n                            \"<\" + component + \">\\n\"\n                        ]\n                    }));\n                } else if (isHtmlCollapsed && lastText !== \"...\") {\n                    lastText = \"...\";\n                    nestedHtmlStack.push(/*#__PURE__*/ (0, _react.createElement)(\"span\", {\n                        ...adjProps,\n                        key: nestedHtmlStack.length,\n                        children: [\n                            spaces,\n                            \"...\\n\"\n                        ]\n                    }));\n                }\n            }\n        });\n        // Hydration mismatch: text or text-tag\n        if (!isHtmlTagsWarning) {\n            const spaces = \" \".repeat(nestedHtmlStack.length * 2);\n            let wrappedCodeLine;\n            if (hydrationMismatchType === \"text\") {\n                // hydration type is \"text\", represent [server content, client content]\n                wrappedCodeLine = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_react.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html--diff-remove\": true,\n                            children: spaces + ('\"' + firstContent + '\"\\n')\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html--diff-add\": true,\n                            children: spaces + ('\"' + secondContent + '\"\\n')\n                        })\n                    ]\n                }, nestedHtmlStack.length);\n            } else {\n                // hydration type is \"text-in-tag\", represent [parent tag, mismatch content]\n                wrappedCodeLine = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_react.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html--tag-adjacent\": true,\n                            children: spaces + (\"<\" + secondContent + \">\\n\")\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html--diff-remove\": true,\n                            children: spaces + ('  \"' + firstContent + '\"\\n')\n                        })\n                    ]\n                }, nestedHtmlStack.length);\n            }\n            nestedHtmlStack.push(wrappedCodeLine);\n        }\n        return nestedHtmlStack;\n    }, [\n        componentStackFrames,\n        isHtmlCollapsed,\n        firstContent,\n        secondContent,\n        isHtmlTagsWarning,\n        hydrationMismatchType,\n        MAX_NON_COLLAPSED_FRAMES\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-container-errors-pseudo-html\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                tabIndex: 10,\n                \"data-nextjs-container-errors-pseudo-html-collapse\": true,\n                onClick: ()=>toggleCollapseHtml(!isHtmlCollapsed),\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_CollapseIcon.CollapseIcon, {\n                    collapsed: isHtmlCollapsed\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"pre\", {\n                ...props,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                    children: htmlComponents\n                })\n            })\n        ]\n    });\n}\n_c = PseudoHtmlDiff;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=component-stack-pseudo-html.js.map\nvar _c;\n$RefreshReg$(_c, \"PseudoHtmlDiff\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/component-stack-pseudo-html.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.js ***!
  \**********************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CallStackFrame\", ({\n    enumerable: true,\n    get: function() {\n        return CallStackFrame;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _stackframe = __webpack_require__(/*! ../../helpers/stack-frame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js\");\nconst _useopenineditor = __webpack_require__(/*! ../../helpers/use-open-in-editor */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../../components/hot-linked-text */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/hot-linked-text/index.js\");\nconst CallStackFrame = function CallStackFrame(param) {\n    let { frame } = param;\n    var _frame_originalStackFrame;\n    // TODO: ability to expand resolved frames\n    // TODO: render error or external indicator\n    const f = (_frame_originalStackFrame = frame.originalStackFrame) != null ? _frame_originalStackFrame : frame.sourceStackFrame;\n    const hasSource = Boolean(frame.originalCodeFrame);\n    const open = (0, _useopenineditor.useOpenInEditor)(hasSource ? {\n        file: f.file,\n        lineNumber: f.lineNumber,\n        column: f.column\n    } : undefined);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-call-stack-frame\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h3\", {\n                \"data-nextjs-frame-expanded\": Boolean(frame.expanded),\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                    text: f.methodName\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                \"data-has-source\": hasSource ? \"true\" : undefined,\n                tabIndex: hasSource ? 10 : undefined,\n                role: hasSource ? \"link\" : undefined,\n                onClick: open,\n                title: hasSource ? \"Click to open in your editor\" : undefined,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                        children: (0, _stackframe.getFrameSource)(f)\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                                d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"polyline\", {\n                                points: \"15 3 21 3 21 9\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"line\", {\n                                x1: \"10\",\n                                y1: \"14\",\n                                x2: \"21\",\n                                y2: \"3\"\n                            })\n                        ]\n                    })\n                ]\n            })\n        ]\n    });\n};\n_c = CallStackFrame;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=CallStackFrame.js.map\nvar _c;\n$RefreshReg$(_c, \"CallStackFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/GroupedStackFrames.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/GroupedStackFrames.js ***!
  \**************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"GroupedStackFrames\", ({\n    enumerable: true,\n    get: function() {\n        return GroupedStackFrames;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _CallStackFrame = __webpack_require__(/*! ./CallStackFrame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.js\");\nconst _CollapseIcon = __webpack_require__(/*! ../../icons/CollapseIcon */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CollapseIcon.js\");\nconst _FrameworkIcon = __webpack_require__(/*! ../../icons/FrameworkIcon */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/FrameworkIcon.js\");\nfunction FrameworkGroup(param) {\n    let { framework, stackFrames } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"details\", {\n        \"data-nextjs-collapsed-call-stack-details\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"summary\", {\n                tabIndex: 10,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_CollapseIcon.CollapseIcon, {}),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_FrameworkIcon.FrameworkIcon, {\n                        framework: framework\n                    }),\n                    framework === \"react\" ? \"React\" : \"Next.js\"\n                ]\n            }),\n            stackFrames.map((frame, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_CallStackFrame.CallStackFrame, {\n                    frame: frame\n                }, \"call-stack-\" + index))\n        ]\n    });\n}\n_c = FrameworkGroup;\nfunction GroupedStackFrames(param) {\n    let { groupedStackFrames, show } = param;\n    if (!show) return;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: groupedStackFrames.map((stackFramesGroup, groupIndex)=>{\n            // Collapse React and Next.js frames\n            if (stackFramesGroup.framework) {\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(FrameworkGroup, {\n                    framework: stackFramesGroup.framework,\n                    stackFrames: stackFramesGroup.stackFrames\n                }, \"call-stack-framework-group-\" + groupIndex);\n            }\n            return stackFramesGroup.stackFrames.map((frame, frameIndex)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_CallStackFrame.CallStackFrame, {\n                    frame: frame\n                }, \"call-stack-\" + groupIndex + \"-\" + frameIndex));\n        })\n    });\n}\n_c1 = GroupedStackFrames;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=GroupedStackFrames.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"FrameworkGroup\");\n$RefreshReg$(_c1, \"GroupedStackFrames\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/GroupedStackFrames.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/component-stack-pseudo-html.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/component-stack-pseudo-html.js ***!
  \***********************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PseudoHtmlDiff\", ({\n    enumerable: true,\n    get: function() {\n        return PseudoHtmlDiff;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _CollapseIcon = __webpack_require__(/*! ../../icons/CollapseIcon */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CollapseIcon.js\");\nfunction getAdjacentProps(isAdj) {\n    return {\n        \"data-nextjs-container-errors-pseudo-html--tag-adjacent\": isAdj\n    };\n}\nfunction PseudoHtmlDiff(param) {\n    let { componentStackFrames, firstContent, secondContent, hydrationMismatchType, ...props } = param;\n    const isHtmlTagsWarning = hydrationMismatchType === \"tag\";\n    // For text mismatch, mismatched text will take 2 rows, so we display 4 rows of component stack\n    const MAX_NON_COLLAPSED_FRAMES = isHtmlTagsWarning ? 6 : 4;\n    const shouldCollapse = componentStackFrames.length > MAX_NON_COLLAPSED_FRAMES;\n    const [isHtmlCollapsed, toggleCollapseHtml] = (0, _react.useState)(shouldCollapse);\n    const htmlComponents = (0, _react.useMemo)(()=>{\n        const tagNames = isHtmlTagsWarning ? [\n            firstContent.replace(/<|>/g, \"\"),\n            secondContent.replace(/<|>/g, \"\")\n        ] : [];\n        const nestedHtmlStack = [];\n        let lastText = \"\";\n        const componentStack = componentStackFrames.map((frame)=>frame.component).reverse();\n        // [child index, parent index]\n        const matchedIndex = [\n            -1,\n            -1\n        ];\n        if (isHtmlTagsWarning) {\n            // Reverse search for the child tag\n            for(let i = componentStack.length - 1; i >= 0; i--){\n                if (componentStack[i] === tagNames[0]) {\n                    matchedIndex[0] = i;\n                    break;\n                }\n            }\n            // Start searching parent tag from child tag above\n            for(let i = matchedIndex[0] - 1; i >= 0; i--){\n                if (componentStack[i] === tagNames[1]) {\n                    matchedIndex[1] = i;\n                    break;\n                }\n            }\n        }\n        componentStack.forEach((component, index, componentList)=>{\n            const spaces = \" \".repeat(nestedHtmlStack.length * 2);\n            // const prevComponent = componentList[index - 1]\n            // const nextComponent = componentList[index + 1]\n            // When component is the server or client tag name, highlight it\n            const isHighlightedTag = isHtmlTagsWarning ? index === matchedIndex[0] || index === matchedIndex[1] : tagNames.includes(component);\n            const isAdjacentTag = isHighlightedTag || Math.abs(index - matchedIndex[0]) <= 1 || Math.abs(index - matchedIndex[1]) <= 1;\n            const isLastFewFrames = !isHtmlTagsWarning && index >= componentList.length - 6;\n            const adjProps = getAdjacentProps(isAdjacentTag);\n            if (isHtmlTagsWarning && isAdjacentTag || isLastFewFrames) {\n                const codeLine = /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                    children: [\n                        spaces,\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            ...adjProps,\n                            ...isHighlightedTag ? {\n                                \"data-nextjs-container-errors-pseudo-html--tag-error\": true\n                            } : undefined,\n                            children: \"<\" + component + \">\\n\"\n                        })\n                    ]\n                });\n                lastText = component;\n                const wrappedCodeLine = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_react.Fragment, {\n                    children: [\n                        codeLine,\n                        isHighlightedTag && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html--hint\": true,\n                            children: spaces + \"^\".repeat(component.length + 2) + \"\\n\"\n                        })\n                    ]\n                }, nestedHtmlStack.length);\n                nestedHtmlStack.push(wrappedCodeLine);\n            } else {\n                if (nestedHtmlStack.length >= MAX_NON_COLLAPSED_FRAMES && isHtmlCollapsed) {\n                    return;\n                }\n                if (!isHtmlCollapsed || isLastFewFrames) {\n                    nestedHtmlStack.push(/*#__PURE__*/ (0, _react.createElement)(\"span\", {\n                        ...adjProps,\n                        key: nestedHtmlStack.length,\n                        children: [\n                            spaces,\n                            \"<\" + component + \">\\n\"\n                        ]\n                    }));\n                } else if (isHtmlCollapsed && lastText !== \"...\") {\n                    lastText = \"...\";\n                    nestedHtmlStack.push(/*#__PURE__*/ (0, _react.createElement)(\"span\", {\n                        ...adjProps,\n                        key: nestedHtmlStack.length,\n                        children: [\n                            spaces,\n                            \"...\\n\"\n                        ]\n                    }));\n                }\n            }\n        });\n        // Hydration mismatch: text or text-tag\n        if (!isHtmlTagsWarning) {\n            const spaces = \" \".repeat(nestedHtmlStack.length * 2);\n            let wrappedCodeLine;\n            if (hydrationMismatchType === \"text\") {\n                // hydration type is \"text\", represent [server content, client content]\n                wrappedCodeLine = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_react.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html--diff-remove\": true,\n                            children: spaces + ('\"' + firstContent + '\"\\n')\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html--diff-add\": true,\n                            children: spaces + ('\"' + secondContent + '\"\\n')\n                        })\n                    ]\n                }, nestedHtmlStack.length);\n            } else {\n                // hydration type is \"text-in-tag\", represent [parent tag, mismatch content]\n                wrappedCodeLine = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_react.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html--tag-adjacent\": true,\n                            children: spaces + (\"<\" + secondContent + \">\\n\")\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html--diff-remove\": true,\n                            children: spaces + ('  \"' + firstContent + '\"\\n')\n                        })\n                    ]\n                }, nestedHtmlStack.length);\n            }\n            nestedHtmlStack.push(wrappedCodeLine);\n        }\n        return nestedHtmlStack;\n    }, [\n        componentStackFrames,\n        isHtmlCollapsed,\n        firstContent,\n        secondContent,\n        isHtmlTagsWarning,\n        hydrationMismatchType,\n        MAX_NON_COLLAPSED_FRAMES\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-container-errors-pseudo-html\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                tabIndex: 10,\n                \"data-nextjs-container-errors-pseudo-html-collapse\": true,\n                onClick: ()=>toggleCollapseHtml(!isHtmlCollapsed),\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_CollapseIcon.CollapseIcon, {\n                    collapsed: isHtmlCollapsed\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"pre\", {\n                ...props,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                    children: htmlComponents\n                })\n            })\n        ]\n    });\n}\n_c = PseudoHtmlDiff;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=component-stack-pseudo-html.js.map\nvar _c;\n$RefreshReg$(_c, \"PseudoHtmlDiff\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/component-stack-pseudo-html.js\n"));

/***/ })

}]);